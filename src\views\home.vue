<template>
  <div class="home-container" @contextmenu.prevent="showMenu($event)" @click="cancelClick">
    <img class="home-bg" :src="store.baseInfo?.consoleBackgroundImage" alt="">
    <!-- 头部信息 -->
    <div class="header">
      <div class="left-menu">
        <div class="more-setting" v-if="isWeChat" @click="modalShow.weChat = true">
          <QrcodeOutlined class="more-icon"/>
          <span class="sub-text">公众号</span>
        </div>
        <div class="notice" @click.stop="openNotice">
          <div class="notice-btn">
            <a-badge :count="store.userInfo.notReadCount" size="small">
              <BellOutlined class="notice-Bell" />
            </a-badge>
            <span class="sub-text">消息</span>
          </div>
          <a-drawer 
            width="35%" 
            title="消息通知" 
            :mask="false" 
            :bodyStyle="{padding: '0'}" 
            placement="right" 
            @close="showNoticePopover = false" 
            :open="showNoticePopover"
          >
            <a-list item-layout="horizontal" 
              style="padding-bottom: 20px;"
              :pagination="{
                current:noticeSeach.pageNo,
                pageSize: noticeSeach.pageSize, 
                total: noticeTotal,
                onChange: handlePageChange,
                onShowSizeChange: handleSizeChange,
              }" 
              :data-source="noticeList">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <div style="display: flex; justify-content: space-between">
                        <span>{{ item.createTime }}&nbsp; <a-tag v-if="!item.isRead" color="#f50">新</a-tag></span>
                        <DeleteOutlined style="color: #c24242; cursor: pointer;" @click="delMessage(item)" />
                      </div>
                    </template>
                    <template #description>
                      <span style="white-space: pre-wrap;" v-if="item.noticeType == 1">{{ item.message }}</span>
                      <img v-else :src="item.message" height="60px" alt="">
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-drawer>
        </div>
      </div>
      <div class="right-info" @mouseenter="showPersonalInfo">
        <img src="../assets/image/person_icon.png" alt="">
      </div>
      <div class="personal-box" :class="{ 'personal-opacity': isShowPersonal }" @mouseleave="isShowPersonal = false">
        <div class="p-head">
          <div class="p-name">
            <p class="text_ov1">{{ store.userInfo.nickName || '未知用户' }}</p>
            <a-tag v-if="store.userInfo.roleType == 3" class="p-tag" color="#d85d24">子账号</a-tag>
            <a-tag v-else class="p-tag" color="#d82424">主账号</a-tag>
          </div>
          <div class="p-cluster">
             <span>单位/机构：</span>
             <span>{{ store.userInfo.unit }}</span>
          </div>
        </div>
        <div class="p-info">
          <div class="echart-list">
            <div class="echart-item" v-for="item in store.userInfo.userClusterInfo">
              <p class="echart-title">{{ item.clusterName + '-' + item.groupNameDesc }}</p>
              <div style="width: 100%; height: 100%;" :id="`coreTiemEChart-${item.id}`"></div>
            </div>
          </div>
        </div>
        <div class="p-button">
          <p class="set-item" @click="openProgram({ type: 'personal' })">
            <IdcardOutlined /> 个人中心
          </p>
          <p class="set-item" @click="useStore().outLogin()">
            <SettingOutlined /> 退出登录
          </p>
        </div>
      </div>
    </div>
    <!-- 桌面应用 -->
    <div class="menu-list" @dragover.prevent @drop="handleDrop($event)">
      <div class="menu-item" 
        v-for="(item, index) in appList" 
        @dblclick="dblclickApp(item)"
        @dragstart="dragStart($event, index)" draggable="true"
        :style="{ left: `${item.x}px`, top: `${item.y}px` }"
        >
        <img :src="item.icon" alt="">
        <p class="menu-name">{{ item.name }}</p>
      </div>
    </div>
    <!-- 底部 -->
    <div class="footer">
      <ul class="dock-content">
        <li v-for="item in menuList">
          <div class="icon-wrapper" @click="openProgram(item)">
            <div class="img_wrapper">
              <img :src="item.icon" :title="item.name">
            </div>
            <p class="dock-text">{{ item.name }}</p>
            <div class="bottom-running-tip" v-if="store.working.includes(item.type)"></div>
          </div> 
        </li>
      </ul>
    </div>
    <!-- app -->
    <div v-for="item in appList">
      <component v-if="item.show" ref="appSoftwareRefs" :is="item.component" :softwareInfo="item" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal(item.id)"/>
    </div>
    <!-- 个人主页 -->
    <PersonalUser v-if="modalShow.personal" ref="personalModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('personal')"/>
    <!-- 右键菜单 -->
    <ContextMenu v-if="isShowMenu" :menuPosition="menuPosition" :menus="['refresh']" :transferFile="[]" @menuChange="menuChange"/>
    <!-- 终端 -->
    <TerminalModal v-if="modalShow.terminal" ref="terminalModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('terminal')"/>
    <!-- 文件管理 -->
    <FileManages v-if="modalShow.file" ref="fileModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @openFile="openWebEdit" @close="closeModal('file')"/>
    <!-- 计算课堂 -->
    <CourseModal v-if="modalShow.course" ref="courseModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('course')"/>
    <!-- 作业管理 -->
    <WorkManages v-if="modalShow.work" ref="workModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('work')"/>
    <!-- 计费中心 -->
    <WorkCharging v-if="modalShow.charging" ref="chargingModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('charging')"/>
    <!-- 软件中心 -->
    <SoftwareCenter v-if="modalShow.software" ref="softwareCenterRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @change="getSoftwareList" @close="closeModal('software')"/>
    <!-- 作业提交 -->
    <JobSubmit v-if="modalShow.jobSubmit" ref="jobSubmitModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('jobSubmit')"/>
    <!-- 创建vnc链接 -->
    <VncSubmit v-if="modalShow.vncSubmit" ref="vncSubmitModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('vncSubmit')"/>
    <!-- 编辑器 -->
    <WebEdit v-if="modalShow.webEdit" ref="webEditModalRef" :itemInfo="itemInfo" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('webEdit')" />
    <!-- 提交工单 -->
    <WorkSheet v-if="modalShow.sheet" ref="sheetModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('sheet')"/>
    <!-- 帮助手册 -->
    <HelpModal v-if="modalShow.help" ref="helpModalRef" :levelList="levelList" :changeLevelIndex="changeLevelIndex" @close="closeModal('help')"/>
    <!-- 关注微信公众号 -->
    <weChatPopup v-if="modalShow.weChat" @close="closeModal('weChat')"></weChatPopup>
    <!-- 文件上传 -->
    <GlobalUploader ref="uploadRef" :params="{ currentPath: fileStore.directory }" :clusterInfo="fileStore.clusterInfo" :dataList="fileStore.dataList"  />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef, onUnmounted } from 'vue';
import { SettingOutlined, IdcardOutlined, BellOutlined, DeleteOutlined, QrcodeOutlined } from '@ant-design/icons-vue';
import softwareService from '@/api/software';
import noticeService from '@/api/notice';
import loginService from '@/api/login'
import TerminalModal from '@/components/TerminalModal.vue'
import FileManages from '@/components/FileManages.vue'
import CourseModal from '@/components/CourseModal.vue'
import WorkManages from '@/components/WorkManages.vue'
import WorkCharging from '@/components/WorkCharging.vue'
import JobSubmit from '@/components/JobSubmit.vue'
import VncSubmit from '@/components/VncSubmit.vue'
import PersonalUser from '@/components/PersonalUser.vue'
import ContextMenu from '@/components/ContextMenu.vue'
import SoftwareCenter from '@/components/SoftwareCenter.vue'
import WebEdit from '@/components/WebEdit.vue'
import WorkSheet from '@/components/WorkSheet.vue'
import HelpModal from '@/components/helpModal.vue'
import weChatPopup from '@/components/weChatPopup.vue'
import GlobalUploader from '@/components/GlobalUploader/GlobalUploader.vue'
import { useStore, useFileStore } from '@/store';
import { message } from 'ant-design-vue';
import ketangIcon from '@/assets/image/ketang.png';
import ruanjianIcon from '@/assets/image/ruanjian.png';
import zhongduanIcon from '@/assets/image/zhongduan.png';
import fileIcon from '@/assets/image/file_icon.png';
import zuoyeguanliIcon from '@/assets/image/zuoyeguanli.png';
import chargingIcon from '@/assets/image/charging.png';
import helpIcon from '@/assets/image/charging0.png'
import gongdanIcon from '@/assets/image/gongdan.png';
import zuoyetijiaoIcon from '@/assets/image/zuoyetijiao.png';
import { formatSeconds } from '@/utils/index';
// import { notification } from 'ant-design-vue';
import * as echarts from 'echarts'
const store = useStore();
// 上传管理
const uploadRef = ref();
const fileStore = useFileStore();
const isWeChat = import.meta.env.VITE_APP_WECHAT == 'false' ? false : true;
onMounted(() => {
  getLoopToken();
  getSoftwareList();
  getNoticeList();
  if (isWeChat && !store.userInfo.isUnfollow) {
    modalShow.value.weChat = true;
  }
  fileStore.uploadRef = uploadRef.value;
});

/**导航菜单 */
const menuList = ref([
  { name: '计算课堂', icon: ketangIcon, type: 'course', },
  { name: '软件中心', icon: ruanjianIcon, type: 'software', },
  { name: '终端', icon: zhongduanIcon, type: 'terminal' },
  { name: '文件管理', icon: fileIcon, type: 'file' },
  { name: '作业提交', icon: zuoyetijiaoIcon, type: 'jobSubmit' },
  { name: '作业管理', icon: zuoyeguanliIcon, type: 'work' },
  { name: '计费中心', icon: chargingIcon, type: 'charging' },
  { name: '提交工单', icon: gongdanIcon, type: 'sheet' },
  { name: '帮助中心', icon: helpIcon, type: 'help' },
])

// const gidNumber = ref(store.userInfo.userClusterInfo[0].gidNumber)
// 当前集群
// const clusterInfo = computed(() => {
//   return store.userInfo.userClusterInfo.find((item: any) => item.gidNumber == gidNumber.value) || {};
// })

// const progress = computed(() => {
//   if (Number(clusterInfo.value.totalCoreTime) > 0 && Number(clusterInfo.value.residueCoreTime) > 0 ) {
//     return parseInt(clusterInfo.value.residueCoreTime) / parseInt(clusterInfo.value.totalCoreTime) * 100;
//   };
//   return 0;
// });

/**获取富文本 */
function getPlainText(htmlContent: string) {
  // 创建一个新的DOM解析器
  var parser = new DOMParser();
  // 使用解析器解析HTML内容，并获取文档对象
  var doc = parser.parseFromString(htmlContent, 'text/html');
  // 创建一个文本节点的范围
  var range = doc.createRange();
  // 选择文档中的所有节点
  range.selectNodeContents(doc.body);
  // 使用范围提取文本内容
  return range.toString();
}

/**个人信息 */
const isShowPersonal = ref(false);
/**剩余核时仪表盘 */
function showPersonalInfo() {
  isShowPersonal.value = true;
  for(let item of store.userInfo.userClusterInfo) {
    // 是否子用户使用主账户核时
    if (store.userInfo.roleType == 3) {
      item.residueCoreTime = item.parentCanUsedCoreTime;
      item.totalCoreTime = item.parentTotalCoreTime;
      item.usedCoreTime = item.usedParentCoreTime;
    }
    echarts.dispose(document.getElementById(`coreTiemEChart-${item.id}`) as HTMLElement)
    echarts.init(document.getElementById(`coreTiemEChart-${item.id}`)).setOption({
      tooltip: {
        confine: true,
        formatter: `
        集群分组：${item.clusterName}-${item.groupNameDesc}
        <br/>
        已用核时：${formatSeconds(item.usedCoreTime || 0, '时')}
        <br/>
        累计核时：${formatSeconds(item.totalCoreTime || 0, '时')}`
      },
      series: [
        {
          clockwise: true,
          min: 0,
          max: Number(item.totalCoreTime) || 1,
          name: 'Pressure',
          type: 'gauge',
          radius: '80%', 
          itemStyle: {
            color: Number(item.residueCoreTime) == 0 ?  "#e6a23c" : Number(item.residueCoreTime) < 0 ? '#c4302c' : '#5cb87a',
          },
          axisTick: {
            show: false
          },
          pointer: {
            length: '80%',
            width: 4,
            offsetCenter: [0, '20%']
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          anchor: {
            show: false
          },
          axisLine: {
            lineStyle: {
              width: 12
            }
          },
          detail: {
            valueAnimation: true,
            offsetCenter: [0, '80%'],
            fontSize: 14,
            formatter: `剩余核时\n${getPlainText(formatSeconds(item.residueCoreTime || 0, '时'))}`,
            color: 'inherit'
          },
          progress: {
            show: true,
            roundCap: true,
            width: 12
          },
          data: [
            { value: Number(item.residueCoreTime) }
          ]
        }
      ]
    });
  }
}

/**组件弹窗 */
const modalShow = ref<{[key: string]: boolean;}>({
  personal: false,
  course: false,
  terminal: false,
  file: false,
  work: false,
  charging: false,
  jobSubmit: false,
  vncSubmit: false,
  software: false,
  webEdit: false,
  sheet: false,
  help: false,
  weChat: false
});

/**组件实例 */
const personalModalRef = ref() //个人主页
const terminalModalRef = ref(); //终端
const fileModalRef = ref(); //文件
const courseModalRef = ref(); //课程
const workModalRef = ref(); // 作业管理
const chargingModalRef = ref(); // 计费中心
const jobSubmitModalRef = ref(); // 作业提交
const vncSubmitModalRef = ref() // vnc提交
const softwareCenterRef = ref(); // 软件中心
const webEditModalRef = ref();
const sheetModalRef = ref();
const helpModalRef = ref();
/**打开底部应用 */
async function openProgram(item: any) {
  if (!item.type) return message.warning('暂未开放');
  if(isNaN(parseInt(item.type))) { // 底部应用
    switch (item.type) {
      case 'course': modalShow.value.course ? courseModalRef.value.open() : modalShow.value.course = true; break;
      case 'terminal': 
        // await owePayTips();
        modalShow.value.terminal ? terminalModalRef.value.open() : modalShow.value.terminal = true; 
      break;
      case 'file': 
        // await owePayTips();
        modalShow.value.file ? fileModalRef.value.open() : modalShow.value.file = true; 
      break;
      case 'personal': modalShow.value.personal ? personalModalRef.value.open() : modalShow.value.personal = true; break;
      case 'work': modalShow.value.work ? workModalRef.value.open() : modalShow.value.work = true; break;
      case 'charging': modalShow.value.charging ? chargingModalRef.value.open() : modalShow.value.charging = true; break;
      case 'jobSubmit': modalShow.value.jobSubmit ? jobSubmitModalRef.value.open() : modalShow.value.jobSubmit = true; break;
      case 'vncSubmit': modalShow.value.jobSubmit ? vncSubmitModalRef.value.open() : modalShow.value.vncSubmit = true; break;
      case 'webEdit': modalShow.value.webEdit ? webEditModalRef.value.open() : modalShow.value.webEdit = true; break;
      case 'software': 
      // message.warning('敬请期待')
      modalShow.value.software ? softwareCenterRef.value.open() : modalShow.value.software = true; 
      break;
      case 'sheet': modalShow.value.sheet ? sheetModalRef.value.open() : modalShow.value.sheet = true; break;
      case 'help': modalShow.value.help ? helpModalRef.value.open() : modalShow.value.help = true; break;
    };
  } else {
    appSoftwareRefs.value.forEach((com: any) => {  // 多开软件
      if(com.name == item.type) com.open();
    })
  }
  changeLevelIndex(item.type)
}

const appSoftwareRefs = ref();
/**打开软件 */
function dblclickApp(item: any) {
  if (item.show) {
    if(appSoftwareRefs.value && appSoftwareRefs.value.length) {
      appSoftwareRefs.value.forEach((com: any) => {
        if(com.name == item.id) com.open();
      })
    }
  } else {
    item.show = true;
    menuList.value.push({ name: item.name, icon: item.icon, type: item.id, })
  }
  changeLevelIndex(item.id);
}

const itemInfo = ref();
/**打开编辑器 */
function openWebEdit(data: any) {
  itemInfo.value = data;
  modalShow.value.webEdit = true;
}

/**关闭弹窗 */
function closeModal(type: string) {
  if(isNaN(parseInt(type))) {
    modalShow.value[type] = false;
  } else {
    menuList.value = menuList.value.filter(item => item.type !== type);
    appList.value.forEach((item: any) => {
      if(type == item.id) item.show = false;
    });
  }
}

/**软件列表 */
const appList = ref<any>([
  // { name: '测试作业', x: 16, y: 0 },
  // { name: '测试软件', x: 16, y: 110 },
])

const softwareMap = ref(new Map());
/**获取软件列表 */
async function getSoftwareList(item?: any) {
  const params = {
    name: '',
    pageNo: 1,
    pageSize: 99,
  }
  const { data } = await softwareService.getSoftwareList(store.authInfo.tenantCode, params);
  for(let item of data.list?.list) {
    softwareMap.value.set(item.id, item)
  }
  sortDesk(data, item);
}

/**软件添加、删除后排序、定位 */
function sortDesk(data: any, item: any) {
  if(!data.added?.length) return appList.value = [];
  if(appList.value.length) {
    const addcol = Math.floor(appList.value.length / 6);
    if (item.added) {
      appList.value.push({ 
        ...item,
        show: false,
        x: addcol ? addcol * 110 : 16,
        y: addcol ? (appList.value.length - (addcol * 6)) * 110  : appList.value.length * 110,
        component: shallowRef(VncSubmit)
      });
      return;
    }
    const delIndex = appList.value.findIndex((obj: any) => item.id == obj.id )
    appList.value.splice(delIndex, 1);
  }
  const col = Math.floor(data.added.length / 6);
  appList.value = data.added.map((item: any, index: number) => {
    return { 
      ...item,
      ...softwareMap.value.get(item.id) || {},
      show: false,
      x: col ? col * 110 : 16, 
      y: col ? col * 110  : index * 110,
      component: shallowRef(VncSubmit)
    }
  });
  levelList.value.push(...appList.value.map((item: any) => item.id));
}

const draggedIndex = ref(null);
const offsetX = ref(0);
const offsetY = ref(0);
/**
 * 拖动开始
 * @param event 
 */
function dragStart(event: any, index: any) {
  draggedIndex.value = index;
  offsetX.value = event.clientX - event.target.offsetLeft;
  offsetY.value = event.clientY - event.target.offsetTop;
};

/**
 * 拖动结束
 * @param event 
 */
function handleDrop(event: any) {
  if (draggedIndex.value !== null) {
    const newX = event.clientX - offsetX.value;
    const newY = event.clientY - offsetY.value;
    // 检查新位置是否与其他图标重叠  
    if (!isOverlapping(newX, newY)) {
      const draggedIcon = appList.value[draggedIndex.value];
      draggedIcon.x = newX;
      draggedIcon.y = newY;
    }
    // 重置拖拽状态  
    draggedIndex.value = null;
  }
};

/**
 * 检查是否重叠
 * @param newX 
 * @param newY 
 */
function isOverlapping(newX: number, newY: number) {
  for (let i = 0; i < appList.value.length; i++) {
    if (i !== draggedIndex.value) {
      const item = appList.value[i];
      if ((item.x + 88 >= newX && newX >= item.x - 88) && (item.y + 100 >= newY && newY >= item.y - 100)) {
        console.log("位置重复");
        return true;
      }
    }
  }
  return false;
};

/**弹窗类型层级 */
const levelList = ref(['personal', 'webEdit', ...menuList.value.map((item: any) => item.type)])
/*层级排序 */
function changeLevelIndex(type: string) {
  levelList.value.splice(levelList.value.indexOf(type), 1);
  levelList.value.push(type);
}

const menuPosition = ref({ x: 0, y: 0 });
const isShowMenu = ref(false);
/**右键菜单 */
function showMenu(event: any) {
  menuPosition.value.x = event.clientX;
  menuPosition.value.y = event.clientY;
  isShowMenu.value = true;
};

/**刷新 */
function menuChange() {
  isShowMenu.value = true;
  window.location.reload();
}

/**欠费提示 */
// function owePayTips(clusterInfo: any) {
//   return new Promise((resolve, reject) => {
//     if (Number(clusterInfo.residueCoreTime) > 0 || store.userInfo.sonCanUseCoreTime) return resolve(true);
//     notification["warning"]({
//       message: `余额提示`,
//       description: '您的该分区余额核时不足，请及时联系管员理充值！',
//       placement: 'top',
//     });
//     reject(false);
//   })
// }

const showNoticePopover = ref(false);
/**通知弹窗 */
async function openNotice() {
  showNoticePopover.value = true;
  await getNoticeList();
  noticeService.readNoticeAll(store.authInfo.tenantCode);
}

const noticeList = ref();
const noticeTotal = ref(0);
const noticeSeach = ref({
  pageNo: 1,
  pageSize: 10
});
/**获取通知列表 */
async function getNoticeList() {
  const { data } = await noticeService.getNoticeList(store.authInfo.tenantCode, noticeSeach.value);
  noticeList.value = data.list || [];
  noticeTotal.value = data.total;
}

/**页改变 */
function handlePageChange(page: number) {
  noticeSeach.value.pageNo = page;
  getNoticeList();
}

/**页数改变 */
function handleSizeChange(current: number, size: number) {
  noticeSeach.value.pageNo = current;
  noticeSeach.value.pageSize = size;
  getNoticeList();
}

/**删除消息 */
async function delMessage(item: any) {
  await noticeService.delNotice(store.authInfo.tenantCode, [item.id]);
  getNoticeList();
  message.success("删除成功")
}

const intervalId = ref();
/**刷新token定时器 */
async function getLoopToken() {
  const loopFn = async () => {
    try {
      const isExpire = (store.authInfo.effectiveTime / 2) * 1000; // 过期时间减半刷新
      const expireTime = new Date(store.authInfo.expireTime).getTime()
      const currentTime = new Date().getTime();
      await loginService.getUserInfo(store.authInfo.tenantCode);
      store.refreshUserTime = currentTime;
      if ((expireTime - currentTime) < isExpire) store.refreshToken()
    } catch (error) {
      console.log("轮询用户信息出错", error);
      // clearInterval(intervalId.value);
    }
  }
  intervalId.value = setInterval(loopFn, 10000);
};

onUnmounted(() => {
  clearInterval(intervalId.value);
})

/**全屏取消 */
function cancelClick() {
  // 取消右键菜单
  if (isShowMenu.value) isShowMenu.value = false;
  // 取消消息抽屉弹窗
  if (showNoticePopover.value) showNoticePopover.value = false
}
</script>

<style lang="scss">
.home-container {
  width: 100%;
  height: 100vh;
  position: relative;
  .home-bg {
    width: 100%;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
  }
  .header {
    height: 60px;
    padding: 0px 20px 0 20px;
    position: relative;

    .right-info {
      cursor: pointer;
      display: flex;
      position: absolute;
      top: 20px;
      right: 40px;

      img {
        height: 40px;
      }

      p {
        line-height: 40px;
        min-width: 45px;
        margin-left: 6px;
        color: #fff;
        margin-bottom: 0;
      }

      z-index: 2;
    }

    .personal-box {
      z-index: 1;
      min-width: 300px;
      max-width: 360px;
      background-color: #f5f5f5;
      opacity: 0;
      border-radius: 12px;
      overflow: hidden;
      position: absolute;
      top: 16px;
      right: 16px;
      transition: all 0.5s;
      pointer-events: none;

      .p-head {
        padding: 16px 18px;
        background-color: #fff;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
  
        .p-name {
          display: flex;
          align-items: center;
          position: relative;
          &>p {
            width: 200px;
            font-size: 18px;
            font-weight: bold;
          }

          .p-tag {
            margin-left: 16px;
            line-height: 16px;
            border-radius: 16px;
            position: absolute;
            top: 30px;
            right: -8px;
          }
        }

        .p-cluster {
          font-size: 14px;
          margin-top: 12px;
          color: #000000;
        }
      }

      .p-info {
        padding: 16px 16px 0px 16px;
        max-height: 400px;
        overflow-y: scroll;
        overflow-x: hidden;
        .echart-list {
          display: flex;
          justify-content: space-around;
          flex-wrap: wrap;
       
          .echart-item {
            // margin-top: 20px;
            width: 160px;
            height: 160px;
            position: relative;
            margin-bottom: 20px;
            // &:not(:nth-child(2n - 1)) {
            //   margin-left: 10px;
            // }
            .echart-title {
              width: 100%;
              text-align: center;
              position: absolute;
              top: -10px;
              color: #555555;
              font-size: 14px;
            }
          }
        }

        .p-line {
          border-bottom: 1px solid #c4c4c4;
          padding-bottom: 16px;
          &>p.nofees {
            color: #ff0000;
          }
          &:last-child {
            border-bottom: none;
          }
        }

        .p-line:nth-child(1) {
          font-weight: bold;
          text-align: center;
          color: #333;

          &>p:nth-child(1) {
            font-size: 14px;
          }

          &>p:nth-child(2) {
            margin-top: 6px;
            font-size: 18px;
          }
        }

        .p-line:nth-child(2) {
          margin-top: 16px;

          .t-line1 {
            color: #333;
            font-size: 12px;
            display: flex;
            span {
              display: inline-block;
              width: 50%;
              text-align: center;
            }
          }

          .t-line2 {
            margin-top: 4px;
            color: #333;
            font-size: 13px;
            display: flex;
            span {
              display: inline-block;
              width: 50%;
              text-align: center;
            }
          }

          .progress {
            margin-bottom: 0;
          }

          :deep(.ant-progress-inner) {
            display: flex;
            justify-content: flex-end;
            border: 1px solid #ad0504;
          }
        }
      }

      .p-button {
        padding: 0 18px;
        padding-top: 16px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        margin-bottom: 16px;
        color: #333;

        .set-item {
          cursor: pointer;

          &:hover {
            color: #1b8fc0;
          }
        }
      }
    }

    .personal-opacity {
      opacity: 1;
      transition: all 0.5s;
      pointer-events: all;
    }
    .left-menu {
      position: absolute;
      top: 20px;
      right: 100px;
      display: flex;
      align-items: center;
      gap: 16px;
      .more-setting {
        cursor: pointer;
        display: flex;
        padding: 10px 5px;
        align-items: center;
        &:hover {
          background-color: #4679b3;
          border-radius: 6px;
        }
        .more-icon {
          font-size: 20px;
          color: #fff;
        }
      }
      .notice {
        padding: 10px 5px;
        cursor: pointer;
        display: flex;
        .notice-btn {
          display: flex;
          align-items: center;
        }
        .notice-Bell {
          cursor: pointer;
          font-size: 22px;
          color: #ffffff;
        }
        &:hover {
          background-color: #4679b3;
          border-radius: 6px;
        }
      }
      .sub-text {
        color: #fff;
        margin-left: 4px;
      }
    }
  }

  .menu-list {
    padding: 0 16px;
    height: calc(100vh - 150px);
    position: relative;

    .menu-item {
      cursor: pointer;
      width: 80px;
      padding: 6px 4px;
      margin-bottom: 10px;
      box-sizing: content-box;
      text-align: center;
      user-select: none;
      /* 防止拖动时选中文字 */
      position: absolute;
      top: 0;
      left: 16px;

      img {
        width: 50px;
        height: 50px;
        border-radius: 50px;
        pointer-events: none;
      }

      .menu-name {
        margin-top: 6px;
        font-size: 14px;
        color: #fff;
        margin-bottom: 0;
      }

      &:hover {
        border-radius: 6px;
        background-color: #fffdfd41;
      }
    }
  }

  .footer {
    user-select: none;
    box-sizing: border-box;
    padding-bottom: 2px;
    transform: translate(50vw);
    width: 0;
    text-align: center;
    height: 90px;
    bottom: 0;
    left: 0;
    transition: all .2s;

    .dock-content {
      height: 80%;
      display: flex;
      transform: translate(-50%);
      position: fixed;
      flex-grow: 0;
      align-items: center;
      border-radius: 8px;
      background: #e4e4e467;
      transition: all .5s;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        cursor: pointer;
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .icon-wrapper {
        margin: 0 6px;
        width: 60px;
        height: 60px;
        transition: all .3s ease;
        position: relative;

        .img_wrapper {
          width: 100%;
          img {
            text-align: center;
            width: 35px;
            height: 35px;
            transition: all .3s ease;
          }
        }

        .dock-text {
          color: #ffffff;
          font-size: 14px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          box-sizing: border-box;
          padding: 0;
          margin-top: 2px;
        }

        &:hover {
          width: 70px;
          transition: all .3s ease;

          .img_wrapper img {
            transform: scale(1.5) translate(0, -6px);
            transition: all .3s ease;
          }
        }
        .bottom-running-tip {
          width: 6px;
          height: 6px;
          border-radius: 6px;
          background-color: #c24242;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translate(-50%, 50%);
        }
      }
    }
  }
}
.ant-list-pagination {
  text-align: center!important;
}
</style>