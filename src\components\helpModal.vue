<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('help')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container">
      <iframe width="100%" height="100%" :src="iframeUrl" frameborder="0"></iframe>
    </div>
    <template #title>
      <div class="modal-head" ref="helpmodalRef">
        <div class="title-text">帮助手册</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('help')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { LineOutlined } from '@ant-design/icons-vue';
import { useDrag } from '../views/hooks/useDrag'
import { useStore } from '@/store';
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const helpmodalRef = ref();
const transformStyle = ref(useDrag(helpmodalRef));

const iframeUrl = ref(import.meta.env.VITE_HELP_URL)
onMounted(() => {
  store.working.push('help');
})

/**打开 */
function open() {
  showModal.value = true;
}

/**隐藏 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('help'), 1);
  emit('close')
};

defineExpose({open, close})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  height: 100%;
}
</style>
