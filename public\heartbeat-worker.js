// heartbeat-worker.js
let heartbeatInterval = 10000; // 默认10秒
let timerId = null;

self.onmessage = function(e) {
  const { command, interval } = e.data;
  
  switch(command) {
    case 'start':
      if (timerId) clearInterval(timerId);
      heartbeatInterval = interval || heartbeatInterval;
      timerId = setInterval(() => {
        postMessage('heartbeat');
      }, heartbeatInterval);
      break;
      
    case 'stop':
      if (timerId) clearInterval(timerId);
      timerId = null;
      break;
      
    case 'updateInterval':
      heartbeatInterval = interval || heartbeatInterval;
      if (timerId) {
        clearInterval(timerId);
        timerId = setInterval(() => {
          postMessage('heartbeat');
        }, heartbeatInterval);
      }
      break;
  }
};