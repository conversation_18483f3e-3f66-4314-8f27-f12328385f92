<template>
  <div class="box" id="details" style="">
    <div class="details_btn" @click="emit('back', false)">返回</div>
    <h1>{{ courseInfo.name }}</h1>
    <div class="content">
      <div>
        <video controls :src="courseInfo.chapters[chapterIndex].periodList[periodIndex].url"></video>
      </div>
      <ul class="list_item">
        <li class="item_list" v-for="(item, index) in courseInfo.chapters">
          <div class="item_list">{{ item.name }}</div>
          <ul class="active">
            <li class="list_item_text" 
              @click="changePeriod(index, childIndex)"
              :class="{'active': chapterIndex == index && periodIndex == childIndex }" 
              v-for="(child, childIndex) in item.periodList"
             > 
              {{ child.name }}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
defineProps<{courseInfo: any}>();
const emit = defineEmits(["back"])
const chapterIndex = ref(0);
const periodIndex = ref(0);

/**切换课时 */
function changePeriod(index: number, childIndex: number) {
  chapterIndex.value = index;
  periodIndex.value = childIndex;
}

</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  padding: 30px;
  background: #edeef0;
}

.box_left {
  list-style: none;
  display: flex;
  flex-direction: column;
  width: 180px;
  padding-top: 30px;
  background: #fff;
  border-radius: 10px;

}

.box_right {
  flex: 1;
  list-style: none;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-left: 30px;
  overflow-y: auto;
  background: #fff;
  border-radius: 10px;
  padding: 0 30px 30px 0;
}

.box_left>li {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 23px;
  height: 53px;
  cursor: pointer;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #303133;

}

.box_left>li>span {
  margin-left: 6px;
}

.box_left .active {
  background: #ECF5FF;
  color: #409eff;
}

.box_right>li {
  width: 290px;
  height: 268px;
  background: #FFFFFF;
  box-shadow: 0 0 10px 0 rgb(0 0 0 / 25%);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 30px;
  margin-top: 34px;
  cursor: pointer;
}

.box_right>li>img {
  width: 290px;
  height: 207px;
  object-fit: contain;
}

.box_right>li>p {
  height: 60px;
  line-height: 60px;
  padding: 0 18px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.details_btn {
  width: 100px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #fff;
  background: #409eff;
  cursor: pointer;
}

#details {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
}

#details>h1 {
  font-size: 26px;
  color: #666;
  margin-top: 15px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  margin-top: 20px;
  overflow: hidden;
}

ul {
  list-style: none;
}

.list_item {
  width: 350px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  border: 1px solid #ccc;
  margin-left: 30px;
  padding: 15px 0;
  overflow-y: auto;
  height: 100%;

}

.list_item>li {
  display: flex;
  flex-direction: column;
}

.list_item>li>div {
  min-height: 40px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
}

.list_item>li>ul {
  display: none;
  flex-direction: column;
}

.list_item>li>.active {
  display: flex;
}

.list_item>li>ul>li {
  min-height: 60px;
  display: flex;
  align-items: center;
  padding-left: 30px;
  cursor: pointer;
}

.list_item>li>ul>li:nth-child(n+2) {
  border-top: 1px solid #ccc;
}

.list_item>li>ul>.active {
  background: #409eff;
  color: #FFFFFF;
  border: none;
}

.content>div {
  flex: 1;
  overflow: hidden;
}

.content>div>video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>