import { request } from '@/utils/request.ts'

export default {
  /**
	 * 作业列表
	 * @param {object} params
	 */
	getJobList(tenant: string, params: {
    pageNo: number,
    pageSize: number,
    startTime: string,
    endTime: string,
    state: number,
    tenant: string,
    tenantCode: string,
    tenantId: string,
    jobName: string,
  }) {
	  return request({ url: `/job/v1.0/${tenant}/current/list`, 	method: 'get', params: params	 });
	},

  /**
	 * 提交作业
	 * @param {object} data
	 */
	createdJobInit(tenant: string, data: {
    current_working_directory: string,
    environment: string[],
    job_name: string,
    script: string,
    cpuNum: string,
    nodeNum: string
  }) {
	  return request({ url: `/job/v1.0/${tenant}/init`, 	method: 'post', data: data	 });
	},

  /**
	 * 创建VNC提交
	 * @param {object} data
	 */
	createdVncInit(tenant: string, data: {
    clusterId: string,
    connectId: string,
    current_working_directory: string,
    environment: string[],
    job_name: string,
    script: string,
    cpuNum: string,
    nodeNum: string,
    softwareId: string,
  }) {
	  return request({ url: `/job/v1.0/${tenant}/init-vnc`, 	method: 'post', data: data	 });
	},

  /**
	 * 取消作业
	 * @param {object} data
	 */
	endJob(tenant: string, jobId: string, clusterId: string) {
	  return request({ url: `/job/v1.0/${tenant}/state/${jobId}/${clusterId}`, 	method: 'delete' });
	},

  /**
	 * 历史作业记录
	 * @param {object} params
	 */
	getJobHistoryList(tenant: string, params: {
    pageNo: number,
    pageSize: number,
    softwareId: string,
    jobName: string,
    userName: string,
    startCreateTime: string,
    endCreateTime: string,
    tenantCode: string,
    tenant: string,
  }) {
	  return request({ url: `/job/v1.0/${tenant}/history/list`, 	method: 'get', params: params	 });
	},

  /**
	 * 收藏常用命令
	 * @param {object} params
	 */
	getJobFavoriteList(tenant: string, params: {
    pageNo: number,
    pageSize: number,
    jobName: string,
    userName: string,
    startCreateTime: string,
    endCreateTime: string,
    tenantCode: string,
    tenant: string,
  }) {
	  return request({ url: `/job/v1.0/${tenant}/favorite/history/list`, 	method: 'get', params: params	 });
	},

  /**
	 * 设置记录状态
	 * @param {object} params
	 */
	setJobState(tenant: string, params: {
    id: number,
    state: boolean,
  }) {
	  return request({ url: `/job/v1.0/${tenant}/favorite/${params.id}/${params.state}`, 	method: 'put' });
	},

  /**-------------------------------------------------- 提交工单 --------------------------------------- */
  /**
	 * 获取工单列表
	 * @param {object} params
	 */
	getSheetList(tenant: string, params: {
    pageNo: number,
    pageSize: number,
    title: string,
    state: string,
    startCreateTime: string,
    endCreateTime: string
  }) {
	  return request({ url: `/work-sheet/v1.0/${tenant}/user/list`, 	method: 'get', params: params	 });
	},

 /**
	 * 创建工单
	 * @param {object} data
	 */
	createdSheetInit(tenant: string, data: {
    title: string,
    message: string,
    images: string[]
  }) {
	  return request({ url: `/work-sheet/v1.0/${tenant}/submit`, 	method: 'post', data: data	 });
	},

  /**
	 * 关闭工单
	 * @param {object} data
	 */
	closeSheet(tenant: string, id: string,) {
	  return request({ url: `/work-sheet/v1.0/${tenant}/shutdown/${id}`, method: 'put'	 });
	},

  /**
	 * 退回工单
	 * @param {object} data
	 */
	backSheet(tenant: string, id: string,) {
	  return request({ url: `/work-sheet/v1.0/${tenant}/roll-back/${id}`, method: 'put'	 });
	},

  /**
   * 工单解决
   * @param tenant
   * @param id
   */
  finishSheet(tenant: string, id: string) {
    return request({ url: `/work-sheet/v1.0/${tenant}/finish/${id}`, method: "put" });
  }
}
