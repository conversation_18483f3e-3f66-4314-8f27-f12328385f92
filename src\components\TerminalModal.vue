<template>
  <a-modal
    ref="modalRef"
    style="top: 6%;"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false" 
    :open="showModal" 
    :footer="null" 
    :wrapClassName="isFullModal ? 'full-modal' : ''"
    :zIndex="levelList.indexOf('terminal')"
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    :confirm-loading="openLoading"
    @cancel="close">
    <div class="container" @contextmenu="contextmenu">
      <a-tabs v-if="tabList.length" style="height: 100%;" v-model:activeKey="activeKey" @edit="onTabEdit" @change="tabchange" type="editable-card" size="small">
        <a-tab-pane v-for="pane in tabList" :key="pane.id" :tab="pane.clusterName + '-' + pane.groupNameDesc" :closable="true">
          <div style="height: 100%;" :id="`terminalContainer-${pane.id}`"></div>
        </a-tab-pane>
      </a-tabs>
      <button v-if="!tabList.length" class="open-terminal" @click="showSelectCluster = true">打开会话</button>
    </div>
    <template #title>
      <div class="modal-head" ref="modalTitleRef">
        <div class="title-text">终端</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('terminal')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>

  <a-modal 
    class="remind-modal"
    v-model:open="showSelectCluster" 
    style="width: 300px; top: 20%;" 
    :bodyStyle="{ height: '330px' }"
    :getContainer="() => modalTitleRef"
    :mask="true" title="会话管理" 
    :maskClosable="false"
    :footer="null" >
    <div class="session-list">
      <div class="session-item" 
         @click="clusterIndex = index"
        :class="{ 'session-check':  clusterIndex == index}"
         v-for="(item, index) in store.userInfo.userClusterInfo">
        <span>{{ item.clusterName + '-' + item.groupNameDesc  }}</span>
        <CheckOutlined v-if="clusterIndex == index" class="check-icon" />
      </div>
    </div>
    <a-flex justify="flex-end">
      <a-button size="small" type="primary" class="lianjie" @click="startConnect">连接</a-button>
    </a-flex>
  </a-modal>
</template>

<script setup lang="ts">
import '@xterm/xterm/css/xterm.css'
import { onMounted, onUnmounted, ref, nextTick, watch } from 'vue'
import { LineOutlined, CheckOutlined } from '@ant-design/icons-vue';
import { useDrag } from '../views/hooks/useDrag'
import { useResizeObserver  } from '@vueuse/core'
import { Terminal } from '@xterm/xterm'
import { FitAddon } from '@xterm/addon-fit'
import { WebglAddon } from '@xterm/addon-webgl'
import { AttachAddon } from '@xterm/addon-attach'
import WebSocketClient from '@/utils/webSocket'
import { useStore } from '@/store';
import { contextmenu } from '@/utils/index'
import { message } from 'ant-design-vue';
import { notification, Modal } from 'ant-design-vue';
defineProps<{ levelList: string[], changeLevelIndex:Function, }>();
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(true);
// 选择集群
const showSelectCluster = ref(true);
// const selectLoading = ref(false)
const clusterIndex = ref(0)
// 全屏
const isFullModal = ref(false);
const openLoading = ref(false);
// 拖拽终端
const modalTitleRef = ref();
const transformStyle = ref(useDrag(modalTitleRef));

onMounted(() => {
  store.working.push('terminal');
  window.addEventListener('beforeunload', handleBeforeUnload);
});

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload);
  close();
});

function handleBeforeUnload(event: BeforeUnloadEvent) {
  if (showModal.value && tabList.value.length > 0) {
    event.preventDefault();
    // event.returnValue = '您有一个正在运行的终端会话。确定要离开吗？'; // Deprecated
    return '您有一个正在运行的终端会话。确定要离开吗？'; // Use return statement
  }
}

/**开始连接 */
const activeKey = ref();
const tabList = ref<any[]>([]);
async function startConnect() {
  await owePayTips(store.userInfo.userClusterInfo[clusterIndex.value]);
  const connectId = store.userInfo.userClusterInfo[clusterIndex.value].id;
  if (tabList.value.some((item) => item.id == connectId)) {
    message.warning('该终端连接已存在！')
    return;
  }
  showSelectCluster.value = false;
  activeKey.value = connectId;
  tabList.value.push(store.userInfo.userClusterInfo[clusterIndex.value]);
  nextTick(() => {
    isInitMessage.value = false
    isReceiveSuccess.value = false;
    terminalInit(connectId, document.getElementById(`terminalContainer-${connectId}`) as HTMLElement);
  })
}

/**编辑、删除tab */
function onTabEdit(targetKey: string, action: string) {
  if (action === 'add') { // remove
    showSelectCluster.value = true;
  }
  if (action === 'remove') {
    tabList.value.splice(tabList.value.findIndex((item) => item.id == targetKey), 1);
    activeKey.value = tabList.value[tabList.value.length - 1]?.id;
    terminal.value[targetKey].dispose();
    webSocket.value[targetKey].close();
  }
}

/**切换终端 */
function tabchange(targetKey: string,) {
  setResize(terminal.value[targetKey], webSocket.value[targetKey], terminal.value[targetKey].cols, terminal.value[targetKey].rows);
}

// 终端实例
const terminal = ref<{[key: string]: Terminal}>({});
// 协议实例
const webSocket = ref<{[key: string]: WebSocketClient}>({});
// 是否初始化
const isInitMessage = ref(false);
// 服务器响应成功
const isReceiveSuccess = ref(false);
/**初始化终端 */
function terminalInit(connectid: string, el: HTMLElement) {
  const term = new Terminal({
    lineHeight: 1.2,
    fontSize: 16,
    cursorBlink: true,
    theme: {
      background: '#1e1e1f'
    }
  });
  const socket = new WebSocketClient(connectid)
  const attachAddon = new AttachAddon(socket.ws as WebSocket , {bidirectional: false})
  const webglAddon = new WebglAddon()
  const fitAddon = new FitAddon()
  nextTick(() => {
    term.loadAddon(attachAddon)
    term.loadAddon(webglAddon);
    term.loadAddon(fitAddon);
    term.open(el)
    term.focus(); // 聚焦
    useResizeObserver(el, (event) => { // 窗口尺寸变化时，终端尺寸自适应
      if(!showModal.value || el.id != `terminalContainer-${activeKey.value}`) return
      fitAddon.fit();
      setResize(term, socket, Math.ceil((event[0].contentRect.width / 9.5)), Math.ceil((event[0].contentRect.height / 22)));
    });
    terminal.value[connectid] = term;
    webSocket.value[connectid] = socket;
    handleConnect(term, socket);
  })

}

/**设置窗口大小 */
function setResize(term: Terminal, socket: WebSocketClient, cols: number, rows: number) {
  socket.send(JSON.stringify({
    "operate": "command", 
    "command": `stty columns ${cols} rows ${rows}\r`
  }));
  term.resize(cols, rows);
}
const selection = ref('');
/**连接终端 */
function handleConnect(term: Terminal, socket: WebSocketClient) {
  term.writeln('Connecting...');
  // 发送指令消息
  socket.openCallback = () => {
    socket.send(JSON.stringify({operate:'connect'}));
  };
  // 收到服务消息
  socket.messageCallback = (_data: any) => {
    if (!isReceiveSuccess.value) {
      if (!isInitMessage.value) {
        term.clear();
        term.write(`欢迎使用\x1B[1;3;31m ${store.baseInfo?.portalTitle || '天玑智算云'} \x1B[0m\r\n`);
        term.write('\x1B[38;5;196m     ____  __  _____    ____  ________    ____  __  ______\x1B[0m\r\n');
        term.write('\x1B[38;5;208m    / __ \\/ / / /   |  / __ \\/ ____/ /   / __ \\/ / / / __ \\\x1B[0m\r\n');
        term.write('\x1B[38;5;226m   / /_/ / /_/ / /| | / / / / /   / /   / / / / / / / / / /\x1B[0m\r\n');
        term.write('\x1B[38;5;118m  / ____/ __  / ___ |/ /_/ / /___/ /___/ /_/ / /_/ / /_/ /\x1B[0m\r\n');
        term.write('\x1B[38;5;45m /_/   /_/ /_/_/  |_/_____/\\____/_____/\\____/\\____/_____/\x1B[0m\r\n');
        term.write('\x1B[38;5;165m                                                          \x1B[0m\r\n');
        term.write('\x1B[38;5;196m     ================================================\x1B[0m\r\n\n');

        term.write('\x1B[1;33m温馨提醒:\x1B[0m\r\n');
        term.write('1. 使用 module avail 命令可査看完整的软件列表;\r\n');
        term.write('2. 登录节点用于编译和编辑工作，请勿在登录节点直接运行程序;\r\n');
        term.write('3. 如需要软件移植、安装、兼容性测试，可联系客户经理或工程师;\r\n');
        term.write('4. 平台按核收费，如未满核提交可能出现和其他用户共用节点影响作业性能;\r\n');
        term.write('5. 文件删除无法恢复，请谨慎执行删除，请勿修改家目录权限，避免登录异常;\r\n');
        term.write('6. 请及时验证手机和邮箱，绑定微信公众号接收告警通知信息;\r\n');
        term.write('7. 建议您先小规模测试，可以运行后再正常计算;\r\n\n');

        term.write('\x1B[1;31m平台公告:\x1B[0m\r\n');
        term.write('尊敬的用户:\r\n');
        term.write('    您好!在使用计算节点遇到作业排队时，建议您脚本中资源调度只保留 -n 参数(#SBATCH -n 核数)，\r\n');
        term.write('这样有可能会更快获取到资源。如有任何疑问，可以联系在线支持工程师进行协助。\r\n\n');
        term.write('                                         感谢您的理解与支持!\r\n\n');
        
        term.write('\x1B[31m                                                天玑智算团队\r\n');
        term.write('                                                  2025-08-07\x1B[0m\r\n\n');
        
        term.write("\r\n");
        isInitMessage.value = true;
      }
      setResize(term, socket, term.cols, term.rows);
      isReceiveSuccess.value = true;
    };
  }
  // 断开重连
  socket.reconnectCallback = () => {
    isReceiveSuccess.value = false;
    const attachAddon = new AttachAddon(socket.ws as WebSocket, {bidirectional: false})
    term.loadAddon(attachAddon);
  }
  //键盘输入时的回调函数
  term.onData((data: string) => { 
    socket.send(JSON.stringify({"operate": "command", "command": data}));
  });
  //选中复制
  term.onSelectionChange(function() {
    if (term.hasSelection()) {
      selection.value = term.getSelection();
    }
  });
  //粘贴 ctrl+v
  term.attachCustomKeyEventHandler(function(ev: any) {
    if (ev.keyCode === 86 && ev.ctrlKey) {
      if (selection.value) {
        socket.send(JSON.stringify({"operate": "command", "command": "\x00" + selection.value}));
        selection.value = "";
      }
    }
    // 复制到剪贴板 ctrl+shift+c
    if (ev.keyCode === 67 && ev.ctrlKey && ev.shiftKey) {
      ev.preventDefault(); // 阻止默认行为打开控制台
      if (selection.value) {
        navigator.clipboard.writeText(selection.value).then(() => {
          console.log('复制到剪贴板:', selection.value);
        }).catch(err => {
          console.error('复制失败:', err);
        });
      }
      return false;
    }
    return true;
  });
};

const errorTips = ref<{[key: string]: boolean}>({})
/**核时监听-是否欠费 */
watch(() => store.refreshUserTime, () => {
  const clusterInfo = store.userInfo.userClusterInfo.find((item: { id: string; }) => item.id == activeKey.value);
  if(clusterInfo) {
    // 主账户/子账户剩余核时
    const usedCoreTime = store.userInfo.roleType == 2 ? Number(clusterInfo.residueCoreTime) : Number(clusterInfo.parentCanUsedCoreTime)
    if (usedCoreTime <= 0 && !errorTips.value[clusterInfo.id]) {
      Modal.error({
        class: 'remind-modal',
        title: '核时欠费',
        content: '您的该集群分区余额核时不足，将关闭当前终端连接，请及时联系管理员充值',
        onOk: () => {
          onTabEdit(activeKey.value, 'remove');
          delete errorTips.value[clusterInfo.id];
        },
        getContainer: () => modalTitleRef.value
      });
      errorTips.value[clusterInfo.id] = true
    }
  };
})

/**欠费提示 */
async function owePayTips(clusterInfo: any): Promise<boolean> {
  return new Promise((resolve) => {
    if (Number(clusterInfo.residueCoreTime) > 0 || store.userInfo.sonCanUseCoreTime) return resolve(true)
    notification.warning({
      style: { width: '450px'},
      message: `核时不足`,
      description: '您的该集群分区余额核时不足，请及时联系管理员充值！',
      placement: 'top',
    });
  })
}


/**打开弹窗 */
function open() {
  showModal.value = true;
}

/**隐藏终端 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  isInitMessage.value = false
  isReceiveSuccess.value = false;
  Object.values(webSocket.value).forEach((item) => item.close());
  Object.values(terminal.value).forEach((item) => item.dispose());
  errorTips.value = {};
  store.working.splice(store.working.indexOf('terminal'), 1);
  emit('close')
}

defineExpose({open, close})
</script>

<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  height: 100%;
  cursor:text;
  background-color: #1e1e1f;
  position: relative;
  .open-terminal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 40px;
    background-color: #2b2b2b;
    color: #d3d3d3;
    border: none;
    cursor: pointer;
    border-radius: 2px;
    &:hover {
      color: #fff;
    }
  }
  :deep(.ant-tabs-content) {
    height: 100%;
  }
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
    background-color: #000000;
  }
  :deep(.ant-tabs-nav .ant-tabs-tab-active) {
    border-color: #000000;
    background-color: #008ae1!important;
  }
  :deep(.ant-tabs-nav .ant-tabs-tab) {
    border-radius: unset!important;
    background-color: #000000;
    padding: 2px 6px;
    margin-left: 0!important;
    margin-bottom: 2px;
    .ant-tabs-tab-btn {
      color: #fff;
      font-size: 14px;
    }
    .ant-tabs-tab-remove {
      .anticon-close {
        color: #fff;
      }
    }
  }
  :deep(.ant-tabs-nav-add .anticon-plus) {
    color: #fff;
  } 
}


// 核时提醒
:deep(.remind-modal .ant-modal-content){
  padding: 20px 24px!important;
  font-weight: 500;
}
:deep(.remind-modal .ant-modal-header){
  border-bottom: none;
}

.session-list {
  height: 300px;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  overflow-y: scroll;
  .session-item {
    padding: 8px;
    cursor: pointer;
    // border-bottom: 1px solid #e6e6e6;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    .check-icon {
      margin-right: 16px;
      color: #096bff;
      font-weight: bold;
    }
  }
  .session-check {
    background-color: #edf5fd;
  }
}
.lianjie {
  width: 100px;
  margin-top: 12px;
}
</style>
