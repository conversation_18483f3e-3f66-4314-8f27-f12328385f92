<template>
  <div class="login-container">
    <div class="background">
      <img class="login-bg" :src="store?.baseInfo?.portalBackgroundImage" alt="">
      <div class="gradient-overlay"></div>
      <div class="floating-shapes">
        <div class="shape shape1"></div>
        <div class="shape shape2"></div>
        <div class="shape shape3"></div>
      </div>
    </div>
    <a-alert
      banner
      closable
    >
    <template #message>
        <p>
          尊敬的用户您好，由于集群环境的搬迁升级，对于之前您所在的8581集群的用户在账号后面需<span style="color: red">追加1269</span>，密码保持不变。给您带来的不便深感抱歉！
        </p>
      </template>
    </a-alert>
    <div class="login-content">
      <div class="login-box">
        <div class="login-header">
          <h1>{{ store.baseInfo?.portalTitle }}</h1>
        </div>
        <!-- <a-tabs class="login-tab" v-model:activeKey="loginType" centered>
          <a-tab-pane key="1">
            <template #tab><span><DesktopOutlined />账号</span></template>
          </a-tab-pane>
          <a-tab-pane key="2">
            <template #tab><span><MobileOutlined />手机</span></template>
          </a-tab-pane>
          <a-tab-pane key="3">
            <template #tab><span><MailOutlined />邮箱</span></template>
          </a-tab-pane>
        </a-tabs> -->
        <a-form 
          :model="formState" 
          name="basic" 
          size="large" 
          autocomplete="off"
          @finish="onFinish" 
          @finishFailed="onFinishFailed"
        >
          <a-form-item name="userName" :rules="[{ required: true, message: '请输入账号/手机号/邮箱!' }]">
            <div class="custom-input-wrapper">
              <UserOutlined class="prefix-icon" />
              <a-input 
                v-model:value="formState.userName" 
                placeholder="请输入账号/手机号/邮箱"
                :bordered="false"
              />
            </div>
          </a-form-item>
          <a-form-item name="passWord" :rules="[{ required: true, message: '请输入密码!' }]">
            <div class="custom-input-wrapper">
              <LockOutlined class="prefix-icon" />
              <a-input-password 
                v-model:value="formState.passWord" 
                placeholder="请输入密码"
                :bordered="false"
              />
            </div>
          </a-form-item>
          <div class="form-extra">
            <div class="left">
              <a-checkbox v-model:checked="formState.remember">记住密码</a-checkbox>
            </div>
            <!-- <div class="right">
              <a class="register-link" @click="showRegister = true">注册账号</a>
            </div> -->
          </div>

          <div class="agreement">
            <a-checkbox v-model:checked="formState.agreement" :class="{ 'agreement-error': showAgreementError }">
              我已阅读并同意
            </a-checkbox>
            <a class="agreement-link" @click="openAgreement">《用户协议》</a>
            <span>和</span>
            <a class="agreement-link" @click="openPrivacy">《隐私政策》</a>
          </div>

          <a-form-item>
            <a-button 
              type="primary" 
              :loading="loading" 
              html-type="submit"
              block
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>

        <div class="download">
          <div class="down-item" v-if="!isDesktop">
            <a href="/static/download/files/CloudDesktop-Setup.exe" target="_blank">
              <img src="../assets/image/windows.png" alt="Windows">
              <span>下载 Windows 客户端</span>
            </a>
          </div>
          <div class="down-item">
            <a-popover placement="bottom">
              <template #content>
                <div class="row-link">
                  <a href="/static/download/files/SSLVPNClient_20220121.exe" target="_blank">
                    <span>下载 旧版 VPN </span>
                    <DownloadOutlined class="prefix-icon" />
                  </a>
                </div>
                <div class="row-link">
                  <a href="https://secloud1.ruijie.com.cn/SSLVPNClient" target="_blank">
                    <span>下载 锐捷 VPN </span>
                    <LinkOutlined class="prefix-icon" />
                  </a>
                </div>
              </template>
              <a>
                <img src="../assets/image/vpn.png" alt="VPN">
                <span>VPN 客户端</span>
                <DownOutlined class="prefix-icon" />
              </a>
            </a-popover>
          </div>
        </div>
      </div>
    </div>
    <!-- 注册弹窗 -->
    <UserRegister v-if="showRegister" @close="showRegister = false" />

    <!-- 用户协议组件 -->
    <UserAgreement
      :visible="agreementVisible"
      @update:visible="agreementVisible = $event"
      type="agreement"
    />

    <!-- 隐私政策组件 -->
    <UserAgreement
      :visible="privacyVisible"
      @update:visible="privacyVisible = $event"
      type="privacy"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { reactive, ref, computed } from 'vue';
import loginService from '@/api/login'
import { UserOutlined, LockOutlined, DownOutlined, DownloadOutlined, LinkOutlined } from '@ant-design/icons-vue';
import { useStore } from '@/store';
import UserRegister from '@/components/UserRegister.vue';
import UserAgreement from '@/components/UserAgreement.vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js'
const router = useRouter();
const store = useStore();
const loading = ref(false);
const showRegister = ref(false);
// 是否桌面环境
const isDesktop = computed(() => window.electronAPI ? true : false)

const formState = reactive<any>({
  userName: '',
  passWord: '',
  remember: false,
  agreement: false
});

const showAgreementError = ref(false);
const agreementVisible = ref(false);
const privacyVisible = ref(false);

// 打开用户协议
const openAgreement = () => {
  agreementVisible.value = true;
};

// 打开隐私政策
const openPrivacy = () => {
  privacyVisible.value = true;
};

const key = CryptoJS.enc.Utf8.parse("9CicXAtafRjvGyma");
const iv = CryptoJS.enc.Utf8.parse("9CicXAtafRjvGyma");
function encrypt(password: string) {
  const encrypted = CryptoJS.AES.encrypt(password, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return encrypted.toString();
}

/**
 * 登录
 * @param values 
 */
async function onFinish(values: any) {
  if (!formState.agreement) {
    showAgreementError.value = true;
    message.warning('请勾选协议')
    return;
  }
  showAgreementError.value = false;
  
  try {
    loading.value = true;
    values.passWord = encrypt(values.passWord);
    await loginService.login(values);
    await loginService.getUserInfo(store.authInfo.tenantCode);
    if (formState.remember) {
      localStorage.setItem('savedCredentials', JSON.stringify({
        userName: formState.userName,
        passWord: formState.passWord
      }));
    } else {
      localStorage.removeItem('savedCredentials');
    }
    router.push('/home');
  } catch (error) {
    console.error('Login failed:', error);
  } finally {
    loading.value = false;
  }
}

// 页面加载时检查是否有保存的登录信息
function checkSavedCredentials() {
  const savedCredentials = localStorage.getItem('savedCredentials');
  if (savedCredentials) {
    const { userName, passWord } = JSON.parse(savedCredentials);
    formState.userName = userName;
    formState.passWord = passWord;
    formState.remember = true;
  }
}

// 在组件挂载时检查保存的登录信息
checkSavedCredentials();

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo);
};

</script>

<style scoped lang="scss">
.login-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  
  .background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .login-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      top: 0;
      left: 0;
    }

    .gradient-overlay {
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        135deg,
        rgba(15, 24, 94, 0.082) 0%,
        rgba(84, 94, 196, 0.068) 50%,
        rgba(2, 137, 209, 0.164) 100%
      );
      z-index: 1;
    }

    .floating-shapes {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 2;
      
      .shape {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
        animation: float 15s infinite;
        
        &.shape1 {
          width: 300px;
          height: 300px;
          top: -150px;
          right: -150px;
          animation-delay: 0s;
        }
        
        &.shape2 {
          width: 200px;
          height: 200px;
          bottom: 50%;
          left: -100px;
          animation-delay: -5s;
        }
        
        &.shape3 {
          width: 400px;
          height: 400px;
          bottom: -200px;
          right: 30%;
          animation-delay: -10s;
        }
      }
    }
  }
  
  .login-content {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: end;
    padding-right: 10%;
    align-items: center;
    z-index: 3;
  }

  .login-box {
    width: 420px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.4);
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 35px;
      h1 {
        font-size: 32px;
        color: #ee1e52d3;
        margin-bottom: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        letter-spacing: 1px;
      }
    }

    .login-tab {
      width: 100%;
    }

    .custom-input-wrapper {
      position: relative;
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      padding: 4px 15px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      
      &:hover, &:focus-within {
        background: rgba(255, 255, 255, 0.12);
        border-color: rgba(255, 255, 255, 0.2);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
      }

      .prefix-icon {
        color: rgba(255, 255, 255, 0.8);
        font-size: 18px;
        margin-right: 12px;
      }
      .ant-input-affix-wrapper-lg {
        padding-left: 0;
      }
      :deep(.ant-input) {
        background: transparent;
        padding: 8px 0;
        height: 40px;
        color: black;
        font-size: 15px;
        border: none;
        box-shadow: none !important;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus {
          -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
          transition: background-color 5000s ease-in-out 0s;
          box-shadow: 0 0 0px 1000px transparent inset;
        }
      }

      :deep(.ant-input-password-icon) {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        
        &:hover {
          color: rgba(255, 255, 255, 1);
        }
      }
    }

    :deep(.ant-form-item) {
      margin-bottom: 28px;
    }

    :deep(.ant-form-item-explain-error) {
      color: #ff4d4f;
      font-size: 13px;
      margin-top: 6px;
      margin-left: 4px;
      opacity: 0.9;
    }

    :deep(.ant-form-item-has-error) {
      .custom-input-wrapper {
        border-color: rgba(255, 77, 79, 0.5);
        background: rgba(255, 77, 79, 0.1);
        
        &:hover, &:focus-within {
          border-color: rgba(255, 77, 79, 0.8);
        }
      }
    }

    :deep(.ant-btn) {
      height: 50px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 1px;
      background: linear-gradient(45deg, #242fc975, #f3214f);
      border: none;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        background: linear-gradient(45deg, #186ad6, #e51e1e);
      }
    }

    .form-extra {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 0 2px;

      .register-link {
        color: rgba(255, 255, 255, 0.85);
        font-size: 14px;
        text-decoration: none;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
          color: #ee1e52d3;
          text-shadow: 0 0 8px rgba(238, 30, 82, 0.4);
        }
      }
    }

    :deep(.ant-checkbox-wrapper) {
      color: rgba(255, 255, 255, 0.85);
      
      .ant-checkbox {
        .ant-checkbox-inner {
          background-color: transparent;
          border-color: rgba(255, 255, 255, 0.5);
        }

        &.ant-checkbox-checked {
          .ant-checkbox-inner {
            background-color: #ee1e52d3;
            border-color: #ee1e52d3;
          }
        }

        &:hover {
          .ant-checkbox-inner {
            border-color: #ee1e52d3;
          }
        }
      }
      
      &.agreement-error {
        .ant-checkbox-inner {
          border-color: #ff4d4f !important;
        }
      }
    }

    .agreement {
      margin-bottom: 24px;
      padding: 0 2px;
      display: flex;
      align-items: center;
      gap: 4px;
      color: rgba(255, 255, 255, 0.85);
      font-size: 14px;

      .agreement-link {
        color: #ee1e52d3;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          opacity: 0.8;
          text-shadow: 0 0 8px rgba(238, 30, 82, 0.4);
        }
      }
    }

    .download {
      margin-top: 24px;
      display: flex;
      gap: 16px;
      justify-content: center;

      .down-item {
        a {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          color: rgba(27, 65, 87, 0.9);
          text-decoration: none;
          transition: all 0.3s ease;
          backdrop-filter: blur(5px);
          
          &:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            
            img {
              transform: scale(1.1);
            }
            
            // span {
            //   color: rgb(231, 21, 21);
            // }
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          img {
            width: 24px;
            height: 24px;
            transition: transform 0.3s ease;
          }

          span {
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            transition: color 0.3s ease;
          }
        }
      }
    }
  }
}
.row-link {
  padding: 8px;
  background: rgba(255, 255, 255, 0.15);
  &:hover {
    background-color: #ebf6ff;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

:deep(.ant-modal-content) {
  .agreement-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0 16px;
    
    h3 {
      margin-bottom: 16px;
      color: #333;
    }
    
    p {
      margin-bottom: 12px;
      line-height: 1.6;
      color: #666;
    }
  }

  .ant-modal-footer {
    .ant-btn-primary {
      background-color: #ee1e52d3;
      border-color: #ee1e52d3;

      &:hover {
        background-color: #d41a49;
        border-color: #d41a49;
      }
    }
  }
}
</style>
