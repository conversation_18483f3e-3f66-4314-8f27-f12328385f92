<template>
  <div class="container">
    <div class="nav-title-wrapper">
      <h2>账户设置</h2>
      <p>账户设置提供全局的账户信息管理，包含查看、完善账户信息，账户认证操作，通知列表设置等</p>
    </div>
    <div class="nav-content">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="基础信息">
          <a-form layout="horizontal" :model="formBasic" :label-col="{ span: 3 }" :wrapper-col="{ span: 8 }">
            <a-form-item label="单位/机构">
              <a-input v-model:value="formBasic.unit" disabled placeholder="input placeholder" />
            </a-form-item>
            <a-form-item label="用户名">
              <a-input v-model:value="formBasic.userName" disabled placeholder="input placeholder" />
            </a-form-item>
            <a-form-item label="昵称">
              <a-input v-model:value="formBasic.nickName" disabled placeholder="input placeholder" />
            </a-form-item>
            <a-form-item label="邮箱">
              <a-input v-model:value="formBasic.email" disabled placeholder="input placeholder" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="2" tab="修改密码">
          <a-form layout="horizontal" @finish="submitPwd" :model="formPwd" :label-col="{ span: 3 }" :wrapper-col="{ span: 8 }">
            <a-form-item 
              label="用户名"
              name="userName"
              :rules="[{ required: true, message: '请输入用户名' }]"
            >
              <a-input v-model:value="formPwd.userName" placeholder="请输入用户名" disabled />
            </a-form-item>
            <a-form-item 
              label="旧密码"
              name="oldPassword"
              :rules="[{ required: true, message: '请输入旧密码' }]"
            >
              <a-input v-model:value="formPwd.oldPassword" placeholder="请输入旧密码" />
            </a-form-item>
            <a-form-item 
              label="新密码"
              name="passWord"
              :rules="[{ required: true, message: '请输入新密码' }]"
            >
              <a-input v-model:value="formPwd.passWord" placeholder="请输入新密码" />
            </a-form-item>
            <a-form-item 
              label="确认密码"
              name="passWordAgain"
              :rules="[{ required: true, message: '请输入再次输入密码' }]"
            >
              <a-input v-model:value="formPwd.passWordAgain" placeholder="再次输入密码" />
            </a-form-item>
            <a-form-item :wrapper-col="{ span: 8, offset: 3 }">
              <a-button type="primary" html-type="submit">提交</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="3" tab="修改电话">
          <a-form layout="horizontal" @finish="submitPhoneEmail(1)" :model="phoneData" :label-col="{ span: 3 }" :wrapper-col="{ span: 8 }">
            <a-form-item 
              label="手机号"
              name="phone"
              :rules="[{ required: true, message: '请输入电话' }]"
            >
              <a-input v-model:value="phoneData.phone" placeholder="请输入电话" />
            </a-form-item>
            <a-form-item 
              label="验证码"
              name="code"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
            <a-input-group compact style="display: flex;">
              <a-input v-model:value="phoneData.code" placeholder="请输入验证码" />
              <a-button v-if="!isPhoneTimeing" @click="sendPhoneCode">获取验证码</a-button>
              <a-button v-else>{{phoneTime}}S</a-button>
            </a-input-group>
            </a-form-item>
            <a-form-item :wrapper-col="{ span: 8, offset: 3 }">
              <a-button type="primary" html-type="submit">提交</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="4" tab="修改邮箱">
          <a-form layout="horizontal" @finish="submitPhoneEmail(2)" :model="emailData" :label-col="{ span: 3 }" :wrapper-col="{ span: 8 }">
            <a-form-item 
              label="邮箱"
              name="email"
              :rules="[{ required: true, message: '请输入邮箱' }]"
            >
              <a-input v-model:value="emailData.email" placeholder="请输入邮箱" />
            </a-form-item>
            <a-form-item 
              label="验证码"
              name="code"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
              <a-input-group compact style="display: flex;">
                <a-input v-model:value="emailData.code" placeholder="请输入验证码" />
                <a-button v-if="!isEmailTimeing" @click="sendEmailCode">获取验证码</a-button>
                <a-button v-else>{{emailTime}}S</a-button>
              </a-input-group>
            </a-form-item>
            <a-form-item :wrapper-col="{ span: 8, offset: 3 }">
              <a-button type="primary" html-type="submit">提交</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
  import loginService from '@/api/login'
  import accountService from '@/api/account'
  import { computed, ref } from 'vue'
  import { message } from 'ant-design-vue';
  import { useStore } from '@/store';

  const store = useStore();
  const activeKey = ref('1');
  /**基础信息 */
  const formBasic = ref({
    userName: store.userInfo.userName,
    tenantName: store.userInfo.tenantName,
    nickName: store.userInfo.nickName,
    email: store.userInfo.email,
    unit: store.userInfo.unit,
  });
  /**修改密码 */
  const formPwd = ref({
    userName: store.userInfo.userName,
    oldPassword: '',
    passWord: '',
    passWordAgain: ''
  });

  /**更改密码 */
  async function submitPwd(values: any) {
    if(values.passWord !== values.passWordAgain) {
      message.error('两次输入的密码不一致');
      return;
    }
    delete values.passWordAgain;
    await loginService.uploadPwd(values);
    useStore().userInfo.userName = values.userName;
    formPwd.value.oldPassword = '';
    formPwd.value.passWord = '';
    formPwd.value.passWordAgain = '';
    message.success('修改密码成功');
  }

  const phoneData = ref({
    type: 1,
    userId: store.userInfo.id,
    phone: "",
    code: ""
  });

  const isCodePhone = computed(() => {
		return /^1[3-9]\d{9}$/.test(phoneData.value.phone)
	})
  const phoneTime = ref(60)//倒计时
  const isPhoneTimeing = ref(false);
	/**发送手机验证码 */
	async function sendPhoneCode() {
		if (isPhoneTimeing.value) return;
		if (isCodePhone.value) {
			await accountService.sendPhoneCode(phoneData.value);
      message.success('发送成功')
			isPhoneTimeing.value = true;
			let interval = setInterval(() => {
				--phoneTime.value
			}, 1000)
			setTimeout(() => {
				clearInterval(interval)
				isPhoneTimeing.value = false;
				phoneTime.value = 60
			}, 60000)
			return;
		}
    message.warning('请输入正确的手机号')
	}

  const emailData = ref({
    type: 2,
    userId: store.userInfo.id,
    email: "",
    code: ""
  });
  // 邮箱校验
  const isCodeEmail = computed(() => {
		return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailData.value.email)
	})
  const emailTime = ref(60)//倒计时
  const isEmailTimeing = ref(false);
	/**发送邮箱验证码 */
	async function sendEmailCode() {
		if (isEmailTimeing.value) return;
		if (isCodeEmail.value) {
			await accountService.sendEmailCode(emailData.value);
      message.success('发送成功')
			isEmailTimeing.value = true;
			let interval = setInterval(() => {
				--emailTime.value
			}, 1000)
			setTimeout(() => {
				clearInterval(interval)
				isEmailTimeing.value = false;
				emailTime.value = 60
			}, 60000)
			return;
		}
    message.warning('请输入正确的邮箱')
	}

  /**修改电话/邮箱 */
  async function submitPhoneEmail(type: number) {
    await accountService.resetUserModify(store.authInfo.tenantCode, type == 1 ? phoneData.value : emailData.value);
    message.success('操作成功');
    if (type == 1) {
      phoneData.value.phone = "";
      phoneData.value.code = ""
    }
    if (type == 2) {
      emailData.value.email = "";
      emailData.value.code = ""
    }
  }
</script>

<style scoped lang="scss">
  .container {
    height: 100%;
    .nav-title-wrapper {
      height: 120px;
      background-color: #fff;
      border-radius: 6px;
      padding: 10px 20px;
      h2 {
        font-weight: bold;
      }
      p {
        font-size: 16px;
      }
    }
    .nav-content {
      padding: 10px 20px;
      margin-top: 10px;
      height: calc(100% - 10px - 120px);
      background-color: #fff;
      border-radius: 6px;
    }
  }
</style>