import { createApp } from 'vue'
import Antd from 'ant-design-vue';
import uploader from 'vue-simple-uploader';
import 'vue-simple-uploader/dist/style.css'
import 'ant-design-vue/dist/reset.css';
import './style.scss'
import router from './router'
import store from './store'
import App from './App.vue'
import { resize } from './directives/index'
const app = createApp(App);
/** 加载自定义指令 */
app.directive("resize", resize);

app.use(store).use(router).use(Antd).use(uploader).mount('#app')
