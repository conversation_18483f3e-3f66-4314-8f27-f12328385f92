<template>
  <div class="register-box">
    <h1>天玑智算云注册</h1>
    <a-form :model="formState" layout="horizontal" name="basic" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" autocomplete="off"
    @finish="onFinish" @finishFailed="onFinishFailed">
    <a-form-item label="账号" name="username" :rules="[{ required: true, message: '请输入注册账号' }]">
      <a-input v-model:value="formState.username" placeholder="输入账户" />
    </a-form-item>

    <a-form-item label="姓名" name="username" :rules="[{ required: true, message: '请输入您的姓名' }]">
      <a-input v-model:value="formState.username" placeholder="输入您的姓名" />
    </a-form-item>

    <a-form-item label="密码" name="password" :rules="[{ required: true, message: '请输入密码!' }]">
      <a-input-password v-model:value="formState.password" placeholder="输入密码" />
    </a-form-item>

    <a-form-item label="确认密码" name="password" :rules="[{ required: true, message: '请再次确认密码' }]">
      <a-input-password v-model:value="formState.password" placeholder="再次确认密码" />
    </a-form-item>

    <a-form-item label="手机号" name="username" :rules="[{ required: true, message: '请输入您的手机号' }]">
      <a-input v-model:value="formState.username" placeholder="输入您的手机号" />
    </a-form-item>

    <a-form-item label="学校/单位" name="username" :rules="[{ required: true, message: '请输入您的学校/单位' }]">
      <a-input v-model:value="formState.username" placeholder="输入您的学校/单位" />
    </a-form-item>

    <a-flex justify="center" gap="middle">
      <a-button type="primary" html-type="submit">注册</a-button>
      <a-button @click="emit('close')">返回</a-button>
    </a-flex>
  </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
const emit = defineEmits(['close'])
interface FormState {
  username: string;
  password: string;
  remember: boolean;
}

const formState = reactive<FormState>({
  username: '',
  password: '',
  remember: true,
});
const onFinish = (values: any) => {
  console.log('Success:', values);
};

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo);
};
</script>

<style scoped lang="scss">
.register-box {
  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #636363;
    font-style: italic;
  }
}
</style>