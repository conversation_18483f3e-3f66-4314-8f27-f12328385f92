{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "cloud.phadcloud.com", "asar": true, "productName": "天玑智算云", "directories": {"output": "release/${version}"}, "files": ["dist-electron", "!**/node_modules/**"], "mac": {"target": ["dmg"], "artifactName": "${productName}-Mac-${version}-Installer.${ext}"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "dist/favicon.png", "artifactName": "CloudDesktop-Setup.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false}, "linux": {"target": ["AppImage"], "artifactName": "${productName}-Linux-${version}.${ext}"}}