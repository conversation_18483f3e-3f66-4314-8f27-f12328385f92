type BusClass = {
  emit: (name: string) => void;
  on: (name: string, callback:Function) => void
}
type pramsKey = string | number | symbol; 


type list = {
  [key: pramsKey]:Array<Function>
}
type oncelist = {
  [key: pramsKey]: {
    fns: Array<Function>,
    hasFired: boolean
  }
}
class Eventbus implements BusClass {
  private list: list;
  private oncelist: oncelist;
  constructor() {
    this.list = {};
    this.oncelist = {} //只能触发一次的事件
  }

  /**
   * 事件订阅
   * @param name 
   * @param callback 
   */
  public on(name: string, callback: Function) {
    let fn:Array<Function> = this.list[name] || [];
    fn.push(callback);
    this.list[name] = fn;
  }

  /**
   * 一次性事件
   * @param name
   * @param callback 
   */
  public once(name: string, callback:Function) {
    if (!this.oncelist[name]) {
      this.oncelist[name] = {
        fns: [],
        hasFired: false
      }
    }
    this.oncelist[name].fns.push(callback)
  }

  /**
   * 事件发布
   * @param name
   * @param args
   */
  public emit(name: string, ...args: Array<any>) {
    let events:Array<Function> = this.list[name];
    if (events && events.length){
      events.forEach(fn => {
        fn.apply(this, args);
      });
      console.log('多次事件');
    }


    let onceEvents: any = this.oncelist[name];
    if (onceEvents && !onceEvents.hasFired) {
      onceEvents.fns.forEach((fn: Function) => {
        fn.apply(this, args);
      });
      this.oncelist[name].hasFired = true;
      delete this.oncelist[name];
      console.log('单次事件', this.oncelist);
    }
  }

  /**
   * 事件卸载
   * @param name 
   */
  public off(name: string) {
    delete this.list[name];
  }
}
export default new Eventbus();
