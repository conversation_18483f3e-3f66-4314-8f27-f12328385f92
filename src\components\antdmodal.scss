/** 公共弹窗样式**/
:deep(.ant-modal-header) {
  position: relative;
  border-bottom: 1px solid #dadada;
  padding-bottom: 6px;
  margin-bottom: 0px;

  .small-icon {
    cursor: pointer;
    position: absolute;
    top: 16px;
    right: 90px;
    width: 18px;
    height: 18px;
    color: #bebebe;

    &:hover {
      background-color: rgba(0, 0, 0, 0.06);
    }
  }

  .big-icon {
    cursor: pointer;
    width: 18px;
    height: 18px;
    position: absolute;
    top: 10px;
    right: 55px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.06);
    }
  }
}

:deep(.ant-modal-content) {
  padding: 20px 0;
  padding-bottom: 0;
  padding-top: 0;
}

:deep(.ant-modal-close) {
  top: 8px !important;
}

.modal-head {
  padding: 8px 20px 0 20px;
  width: 90%;

  .title-text {
    width: 100%;
    height: 100%;
    font-size: 15px;
    cursor: move;
  }
}
