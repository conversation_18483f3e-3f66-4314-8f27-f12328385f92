<template>
  <a-modal 
    style="top: 6%;"
    :zIndex="levelList.indexOf('personal')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="modal-content" ref="modalContentRef">
      <div class="left">
        <a-menu v-model:selectedKeys="menuKeys" style="width: 100%" mode="inline" :items="menuData" @select="handleClick"></a-menu>
      </div>
      <div class="right">
        <!-- 账户设置 -->
         <UserSetting v-if="menuKeys[0] == 'setting'" />
        <!-- 集群账户 -->
         <ClusterSetting v-if="menuKeys[0] == 'subAccount' && store.userInfo.roleType == 2" :tableScrolly="tableScrolly" />
        <!-- 登录日志 -->
        <LoginSetting v-if="menuKeys[0] == 'log'" :tableScrolly="tableScrolly" />
      </div>
    </div>
    <template #title>
      <div class="modal-head" ref="headModalRef">
        <div class="title-text">个人中心</div>
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('personal')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { h, onMounted, ref, nextTick } from 'vue'
import { FileSearchOutlined, TeamOutlined, ContactsOutlined } from '@ant-design/icons-vue';
import { useResizeObserver  } from '@vueuse/core'
import { useDrag } from '../views/hooks/useDrag'
import { useStore } from '@/store';
import UserSetting from './account/UserSetting.vue'
import LoginSetting from './account/LoginSetting.vue'
import ClusterSetting from './account/ClusterSetting.vue'
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const store = useStore();
const emit = defineEmits(['close']);
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽    
const headModalRef = ref();
const transformStyle = ref(useDrag(headModalRef));

onMounted(() => {
  if (store.userInfo.roleType == 2) {
    menuData.value.push({
      key: 'subAccount',
      icon: () => h(TeamOutlined),
      label: '集群账户',
      title: '集群账户',
    })
  }
})

// 导航栏
const menuKeys = ref(['setting'])
const menuData = ref([
  {
    key: 'setting',
    icon: () => h(ContactsOutlined),
    label: '帐户设置',
    title: '帐户设置',
  },
  {
    key: 'log',
    icon: () => h(FileSearchOutlined),
    label: '登录日志',
    title: '登录日志',
  },
])

const modalContentRef = ref();
const tableScrolly = ref(0);
/**切换 */
async function handleClick(_event: any) {
  if (tableScrolly.value) return; // 第一次打开监听
  nextTick(() => {
    useResizeObserver(modalContentRef.value, (entries) => { //监听窗口
      if(!modalContentRef.value) return;
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - 240;
    });
  })
}

/**弹窗打开 */
function open() {
  showModal.value = true;
};

/**关闭弹窗 */
function close() {
  showModal.value = false;
  emit('close')
};

defineExpose({open, close})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.modal-content {
  background-color: #f2f2f2;
  display: flex;
  padding: 10px;
  height: 100%;
  .left {
    width: 25%;
    background-color: #fff;
    margin-right: 10px;
    border-radius: 6px;
  }
  .right {
    width: 75%;
  }
}
</style>
