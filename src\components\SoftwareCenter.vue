<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('software')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container">
      <div class="card-head">
        <a-form
          size="small"
          layout="inline"
          :model="searchData"
          name="basic"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17}"
          autocomplete="off"
        >
          <a-form-item label="软件名称">
            <a-input v-model:value="searchData.name" @change="getList" placeholder="请输入软件名称" />
          </a-form-item>
        </a-form>
      </div>
      <a-spin tip="加载中..." :spinning="loading">
        <div class="list">
          <div class="item" v-for="item in dataList">
            <img :src="item.icon" alt="">
            <div class="line-name">
              <p class="name text_ov1">{{ item.name }}</p>
              <p @click="setSoftwareDesk(item, )">
                <span class="line-span" :class="{'del': item.added}">{{ !item.added ? '添加' : '移除' }}</span>
              </p>
            </div>
          </div>
        </div>
        <a-empty v-if="!loading && !dataList.length" />
      </a-spin>

    </div>
    <template #title>
      <div class="modal-head" ref="softwaremodalRef">
        <div class="title-text">软件中心</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('software')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import softwareService from '@/api/software';
import { onMounted, ref } from 'vue'
import { LineOutlined } from '@ant-design/icons-vue';
import { useDrag } from '../views/hooks/useDrag'
import { useStore } from '@/store';
import { message } from 'ant-design-vue';
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['change', 'close'])
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const softwaremodalRef = ref();
const transformStyle = ref(useDrag(softwaremodalRef));

// 用户集群
const clusterMap = new Map();
for(let item of store.userInfo.userClusterInfo) {
  clusterMap.set(item.gidNumber, item)
}

onMounted(() => {
  store.working.push('software');
  getList();
})

// 搜索
const searchData = ref<any>({
  name: '',
  pageNo: 1,
  pageSize: 50,
});
const loading = ref(false);
const dataList = ref<any>([]);
const total = ref(0);
/**获取软件列表 */
async function getList() {
  loading.value = true;
  const { data } = await softwareService.getSoftwareList(store.authInfo.tenantCode, searchData.value);
  if (data.list && data.list.list?.length) {
    dataList.value = data.list.list.filter((item: any) => clusterMap.has(item.gidNumber));
    total.value = data.list.total;
  }

  loading.value = false;
}

/** 添加软件*/
async function setSoftwareDesk(item: any) {
  await softwareService.setSoftwareDesk(store.authInfo.tenantCode, {id: item.id, state: !item.added});
  message.success(item.added ? "删除成功" : "添加成功");
  item.added = !item.added;
  emit('change', item)
}

/**打开弹窗 */
function open() {
  showModal.value = true;
}

/**隐藏弹窗 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('software'), 1);
  emit('close');
};

defineExpose({open, close})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  width: 100%;
  margin-top: 12px;
  height: 100%;
  .card-head {
    padding: 0 20px;
  }
  .list {
    overflow: auto;
    flex: 1;
    flex-wrap: wrap;
    justify-content: stretch;
    padding: 10px;
    display: grid;
    width: 100%;
    grid-template-columns: repeat(6,1fr);
    grid-auto-rows: 120px;
    gap: 20px;
    padding: 24px;
    .item {
      width: 100%;
      height: 120px;
      padding: 10px;
      box-shadow: 0 0 10px rgb(238, 246, 255);
      overflow: hidden;
      border-radius: 6px;
      margin-bottom: 12px;
      text-align: center;
      img {
        width: 70px;
        height: 70px;
        border-radius: 70px;
      }
      .line-name {
        margin-top: 6px;
        position: relative;
        &>p:nth-child(2) {
          cursor: pointer;
          position: absolute;
          top: 0;
          right: 0;
        }
        .line-span {
          font-size: 12px;
          color: rgb(68, 135, 235);
          &.del {
            color: #eb2121;
          }
        }
      }
      .name {
        width: 80%;
      }
    }
  }
}
</style>
