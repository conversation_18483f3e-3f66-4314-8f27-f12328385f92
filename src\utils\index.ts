/**
 * 秒数转时分秒
 */
export function formatSeconds(t: any, unit = '小时') {
  if (t == null) {
    return `--秒`;
  }
  const isNegative = Number(t) < 0; // 检查是否为负数
  t = Math.abs(t); // 转为绝对值

  let h: string | number = Math.floor(t / 3600);
  let m: string | number = Math.floor((t % 3600) / 60);
  let s: string | number = Math.floor(t % 60);

  // 三元表达式 补零 如果小于10 则在前边进行补零 如果大于10 则不需要补零
  h = h < 10 ? "0" + h : h;
  m = m < 10 ? "0" + m : m;
  s = s < 10 ? "0" + s : s;

  if (isNegative) {
    if (h !== "00") {
      return `<span style="color: #c4302c;"> - ${h}${unit}${m}分${s}秒</span>`;
    } else if (m !== "00") {
      return `<span style="color: #c4302c;"> - ${m}分${s}秒</span>`;
    } else {
      return `<span style="color: #c4302c;"> - ${s}秒</span>`;
    }
  } else {
    if (h !== "00") {
      // 如果小时不为 0 则返回 小时:分钟:秒
      return `${h}${unit}${m}分${s}秒`;
    } else if (m !== "00") {
      // 如果分钟不为 0 则返回 分钟:秒
      return `${m}分${s}秒`;
    } else {
      // 如果秒数不为 0 则返回 秒
      return `${s}秒`;
    }
  }
}

let clickTime: any = null;
/*** 防止重复点击*/
export function debounceClick() {
	return new Promise((resolve, reject) => {
		if (!clickTime) {
			clickTime = setTimeout(() => { clickTime = null }, 500);
			resolve(true);
			return;
		}
		reject('重复点击');
	})
}

/**右键菜单 */
export function contextmenu(e: MouseEvent) {
  // 获取选中文本
  const selection = window.getSelection()?.toString() || '';
  // 发送给主进程
  window.electronAPI.sendMessageToMain({
    x: e.x,
    y: e.y,
    selectionText: selection,
    editFlags: {
      canCut: selection.length > 0,
      canCopy: selection.length > 0,
      canPaste: true
    }
  });
}

/**
 * 验证文件夹名称是否合法
 * @param {string} folderName - 要验证的文件夹名称
 * @returns {Object} 返回验证结果对象
 *   - isValid: {boolean} 是否合法
 *   - message: {string} 错误提示信息（如果合法则为空字符串）
 */
export function validateFolderName(folderName: string) {
  // 检查是否包含非法字符
  if (/[\\/:*?"<>|¥$%@!#]/.test(folderName)) {
    return {
      isValid: false,
      message: '文件名不能包含特殊字符'
    };
  }
  if (!(/^\S+$/.test(folderName))) {
    return {
      isValid: false,
      message: '文件名不能包含空格'
    };
  }
  
  // 检查开头或结尾是否包含空格
  if (folderName !== folderName.trim()) {
    return {
      isValid: false,
      message: '文件名开头或结尾不能包含空格'
    };
  }
  
  // 检查名称长度（可根据需要调整）
  if (folderName.length > 50) {
    return {
      isValid: false,
      message: '文件名过长，最多允许50个字符'
    };
  }
  
  // 所有检查通过
  return {
    isValid: true,
    message: ''
  };
}
