<template>
  <div class="container">
    <div class="nav-title-wrapper">
      <h2>集群帐户管理</h2>
      <p>申请、切换集群账号</p>
      <p v-if="store.userInfo.roleType == 2">管理所创建的子账号，子账号共用主账号的核时. 最多只可创建20个子账户</p>
    </div>
    <div class="nav-content">
      <a-tabs v-model:activeKey="activeCluster">
        <a-tab-pane key="1" tab="集群账号">
          <a-table
            size="small"
            rowKey="text"
            :loading="subTableloading"
            :scroll="{ x: 500, y: tableScrolly }"
            :columns="clusterColumns.map(column => ({...column, align: 'center'}))"
            :data-source="store.userInfo.userClusterInfo"
            :bordered="true"
            :pagination="false"
          >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'clusterName'">
              {{ record.clusterName + '-' + record.groupNameDesc }}
            </template>
            <template v-if="column.key === 'state'">
              <a-tag color="green">正在使用</a-tag>
            </template>
          </template>
          </a-table>
        </a-tab-pane>
        <template #rightExtra v-if="activeCluster == '2'">
          <a-button size="small" type="primary" @click="addSubAccount">新增账户</a-button>
        </template>
        <a-tab-pane key="2" tab="子账号">
          <a-table
            size="small"
            rowKey="text"
            :loading="subTableloading"
            :scroll="{ x: 500, y: tableScrolly }"
            :columns="subColumns.map(column => ({...column, align: 'center'}))"
            :data-source="childrenUserList"
            :bordered="true"
            :pagination="false"
          >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'state'">
              <a-button size="small" :danger="!record.state" type="primary" @click="updateState(record)">{{ record.state ? '启用' : '禁用' }}</a-button>
            </template>
            <template v-if="column.key === 'usedCoreTime'">
              <span>{{ formatSeconds(record.usedCoreTime, '核时') }}</span>
            </template>
            <template v-if="column.key === 'operate'">
              <a-button size="small" type="link" @click="updateAccount(record)">修改</a-button>
              <a-button size="small" type="link" @click="resAccountPwd(record)">重置密码</a-button>
            </template>
          </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>

  <!-- 新增/修改 -->
  <a-modal v-model:open="openSubAccount" :title="formData.id == undefined ? '新建账户' : '更新账户'" @cancel="restForm" :width="550">
    <a-form layout="horizontal" ref="formRef" :model="formData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="账户" name="userName" :rules="[{ required: true, message: '请输入账户' }]">
        <a-input v-model:value="formData.userName" placeholder="请输入账户" />
      </a-form-item>
      <a-form-item label="姓名" name="nickName" :rules="[{ required: true, message: '请输入姓名' }]">
        <a-input v-model:value="formData.nickName" placeholder="请输入姓名" />
      </a-form-item>
      <a-form-item label="手机号" name="phone" :rules="[{ required: true, trigger: 'blur', validator: checkPhone  }]">
        <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
      </a-form-item>
      <a-form-item label="密码" name="pwd" :rules="[{ required: true, validator: checkPwd }]" v-if="formData.id == undefined">
        <a-input-password v-model:value="formData.pwd" :visible="true" placeholder="请输入密码" />
      </a-form-item>
      <a-form-item label="邮箱" name="email" :rules="[{ required: true,  validator: checkEmail }]">
        <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
      </a-form-item>
      <a-form-item label="单位/学校" name="unit" :rules="[{ required: true, message: '请输入单位/学校' }]">
        <a-input v-model:value="formData.unit" placeholder="请输入单位/学校" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="openSubAccount = false; restForm">取消</a-button>
      <a-button type="primary" :loading="subLoading" @click="submitSub">确定</a-button>
    </template>
  </a-modal>

  <!-- 重置密码 -->
  <a-modal v-model:open="openAccountPwd" :title="'重置密码'" @cancel="formPwdData.password = ''" :width="550">
    <a-form layout="horizontal" ref="formPwdRef" :model="formPwdData" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="新密码" name="password" :rules="[{ required: true, message: '请输入密码' }]">
        <a-input v-model:value="formPwdData.password" placeholder="请输入密码" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="openAccountPwd = false; restForm">取消</a-button>
      <a-button type="primary" :loading="subPwdLoading" @click="submitSubPwd">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import accountService from '@/api/account'
  import { onMounted, ref } from 'vue'
  import { formatSeconds } from '@/utils/index';
  import { message, Modal } from 'ant-design-vue';
  import { useStore } from '@/store';
  import type { Rule } from 'ant-design-vue/es/form';
  defineProps<{ tableScrolly: number }>()
  const store = useStore();

  const activeCluster = ref('1')
  const clusterColumns = [
    { title: '账号分组', dataIndex: 'clusterName', key: 'clusterName' },
    { title: '状态', dataIndex: 'state', key: 'state' },
  ];

  /**子账户表格 */
  const subColumns = [
    { title: '账户', dataIndex: 'userName', key: 'userName' },
    { title: '姓名', dataIndex: 'nickName', key: 'nickName' },
    { title: '单位/学校', dataIndex: 'unit', key: 'unit'},
    { title: '联系方式', dataIndex: 'phone', key: 'phone'},
    // { title: '分区', dataIndex: 'gidNumberDesc', key: 'gidNumberDesc'},
    { title: '状态', dataIndex: 'state', key: 'state'},
    // { title: '已用核时', dataIndex: 'usedCoreTime', key: 'usedCoreTime'},
    { width: 140, title: '操作', dataIndex: 'operate', key: 'operate'},
  ];

  const childrenUserList = ref([]);
  const subTableloading = ref(false);
  /**获取子账户 */
  async function getChildrenUsers() {
    subTableloading.value = true;
    const { data } = await accountService.getChildrenUserList(store.authInfo.tenantCode);
    childrenUserList.value = data;
    subTableloading.value = false;
  }

  const openSubAccount = ref(false);
  /**子账户信息 */
  const formData = ref<any>({
    id: undefined,
    userName: "",
    nickName: "",
    phone: "",
    pwd: "",
    email: "",
    unit: "",
    state: true,
    tenantCode: store.authInfo.tenantCode,
    tenantId: store.authInfo.tenantId,
  });

  /**
   * 生成随机用户名
   */
  function generateUniqueUsername() {
    const timestamp = Date.now().toString().slice(-2); // 取时间戳后2位
    const randomStr = Math.random().toString(36).substr(2, 2); // 2位随机字母
    return `${store.authInfo.tenantCode}${timestamp}${randomStr}`;
  }

  /**
   * 生成11位随机密码
   * @param {object} options 配置项
   * @param {boolean} options.uppercase 包含大写字母 (默认true)
   * @param {boolean} options.lowercase 包含小写字母 (默认true)
   * @param {boolean} options.numbers 包含数字 (默认true)
   * @param {boolean} options.symbols 包含特殊符号 (默认false)
   */
  function generateCustomPassword(options = {}) {
    const config = {
      uppercase: true, // 默认启用大写字母
      lowercase: true, // 默认启用小写字母
      numbers: true, // 默认启用数字
      symbols: false, // 默认不启用特殊符号
      passwordLength: 11, // 默认密码长度为 11 位
      ...options
    };

    const charSets = {
      uppercase: "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
      lowercase: "abcdefghijklmnopqrstuvwxyz",
      numbers: "0123456789",
      symbols: "!@#$%^&*_+-="
    };

    // 确保至少包含每种字符
    let password = "";
    if (config.uppercase) password += charSets.uppercase[Math.floor(Math.random() * charSets.uppercase.length)];
    if (config.lowercase) password += charSets.lowercase[Math.floor(Math.random() * charSets.lowercase.length)];
    if (config.numbers) password += charSets.numbers[Math.floor(Math.random() * charSets.numbers.length)];
    if (config.symbols) password += charSets.symbols[Math.floor(Math.random() * charSets.symbols.length)];

    // 构建字符池
    let charPool = "";
    if (config.uppercase) charPool += charSets.uppercase;
    if (config.lowercase) charPool += charSets.lowercase;
    if (config.numbers) charPool += charSets.numbers;
    if (config.symbols) charPool += charSets.symbols;

    // 填充剩余长度
    const remainingLength = config.passwordLength - password.length;
    for (let i = 0; i < remainingLength; i++) {
      password += charPool[Math.floor(Math.random() * charPool.length)];
    }

    // 打乱密码字符顺序
    return password
      .split("")
      .sort(() => Math.random() - 0.5)
      .join("");
  }

  /**添加子账户 */
  function addSubAccount() {
    formData.value.userName = generateUniqueUsername();
    formData.value.pwd = generateCustomPassword();
    openSubAccount.value = true;
  }

  /**校验密码 */
  const checkPwd = async (_rule: Rule, value: string) => {
    if (!value) {
      return Promise.reject('请输入密码');
    };
    if(!(/^[^\u4e00-\u9fa5]+$/.test(value))) {
      return Promise.reject('不能包含中文');
    }
    if (value.length < 6) {
      return Promise.reject('请输入长度6位以上密码');
    }
    return Promise.resolve();
  };

  /**校验邮箱 */
  const checkEmail = async (_rule: Rule, value: string) => {
    if (!value) {
      return Promise.reject('请输入邮箱');
    };
    if(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('请输入正确邮箱');
    }
  };

  /**校验手机号 */
  const checkPhone = async (_rule: Rule, value: string) => {
    if (!value) {
      return Promise.reject('请输入手机号');
    };
    if(/^1[3-9]\d{9}$/.test(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('请输入正确手机号');
    }
  };

  const formRef = ref();
  const subLoading = ref(false);
  /**提交表单 */
  async function submitSub() {
    try {
      subLoading.value = true;
      await formRef.value.validate();
      if (formData.value.id) {
        await accountService.updateChildrenUser(store.authInfo.tenantCode, formData.value);
      } else {
        await accountService.createChildrenUser(store.authInfo.tenantCode, formData.value);
      }
      getChildrenUsers();
      restForm();
      subLoading.value = false;
      openSubAccount.value = false;
      message.success('提交成功');
    } catch (error) {
      console.log(error);
      subLoading.value = false;
    }
  }

  /**修改子用户 */
  function updateAccount(item: any) {
    for(let key in formData.value) {
      formData.value[key] = item[key];
    };
    openSubAccount.value = true;
  }

  /**重置表单 */
  function restForm() {
    formData.value = {
      id: undefined,
      userName: "",
      nickName: "",
      phone: "",
      pwd: "",
      email: "",
      unit: "",
      state: true,
      tenantCode: store.authInfo.tenantCode,
      tenantId: store.authInfo.tenantId,
    }
  }

  /**启用/禁用 */
  function updateState(item: any) {
    Modal.confirm({
      title: '提示',
      content: `确定要${item.state ? '禁用' : '启用'}${item.nickName}该用户吗`,
      onOk: async () => {
        return accountService.updateState(store.authInfo.tenantCode, item.id, item.state ? 0 : 1).then(() => {
          message.success('操作成功');
          getChildrenUsers();
        })
      }
    })
  }

  const curPwdItem = ref();
  // 打开重置密码
  function resAccountPwd(item: any) {
    curPwdItem.value = item;
    openAccountPwd.value = true;
  }

  const openAccountPwd = ref(false);
  const subPwdLoading = ref(false);
  // 表单
  const formPwdRef = ref();
  // 密码
  const formPwdData = ref({ password: "" });
  // 重置子用户密码
  async function submitSubPwd() {
    try {
      subPwdLoading.value = true;
      await formPwdRef.value.validate();
      await accountService.resetUserPwd({
        userId: curPwdItem.value.id,
        password: formPwdData.value.password
      });
      subPwdLoading.value = false;
      openAccountPwd.value = false;
      getChildrenUsers();
      message.success('修改密码成功');
    } catch (error) {
      subPwdLoading.value = false;
    }
  }

  onMounted(() => {
    getChildrenUsers()
  })
</script>

<style lang="scss" scoped>
  .container {
    height: 100%;
    .nav-title-wrapper {
      height: 120px;
      background-color: #fff;
      border-radius: 6px;
      padding: 10px 20px;
      h2 {
        font-weight: bold;
      }
      p {
        font-size: 16px;
      }
    }
    .nav-content {
      padding: 10px 20px;
      margin-top: 10px;
      height: calc(100% - 10px - 120px);
      background-color: #fff;
      border-radius: 6px;
    }
  }
</style>