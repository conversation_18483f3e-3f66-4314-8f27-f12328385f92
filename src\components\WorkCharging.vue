<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('charging')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container" ref="fileboxRef" @contextmenu.prevent="showMenu($event)" @click="isShowMenu = false">
      <div class="card-head">
        <a-form
          size="small"
          layout="inline"
          :model="searchData"
          name="basic"
          :label-col="{ span: 7 }"
          :wrapper-col="{ span: 17}"
          autocomplete="off"
        >
          <a-form-item label="作业名称">
            <a-input v-model:value="searchData.jobName" @change="getList" placeholder="请输入名称" />
          </a-form-item>
          <a-form-item label="提交时间">
            <a-range-picker v-model:value="dateArr" valueFormat="YYYY-MM-DD" @change="dateChange" />
          </a-form-item>
          <a-form-item>
            <a-button size="small" type="primary" @click="getList">
              <ReloadOutlined />刷新
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="card">
        <a-table
          size="small"
          rowKey="text"
          :loading="loading"
          :scroll="{ x: 1200,  y: tableScrolly }"
          :columns="columns.map(column => ({...column, align: 'center'}))"
          :data-source="dataList"
          :bordered="true"
          :pagination="{
            current: searchData.pageNo,
            pageSize: searchData.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: any) => `共 ${total} 条`,
            onChange: handlePageChange,
            onShowSizeChange: handleSizeChange,
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'consumeTime'">
              <span>{{ formatSeconds(record.consumeTime) }}</span>
            </template>
            <template v-if="column.key === 'coreTime'">
              <span>{{ formatSeconds(record.coreTime, '核时') }}</span>
            </template>
            <template v-if="column.key === 'price'">
              <span>{{ '¥ ' + parseFloat(record.price)?.toFixed(5) }}</span>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <template #title>
      <div class="modal-head" ref="headModalRef">
        <div class="title-text">计费中心</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('charging')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
  <ContextMenu v-if="isShowMenu" :menuPosition="menuPosition" :menus="menus" :transferFile="[]" @menuChange="menuChange"></ContextMenu>
</template>
<script setup lang="ts">

import { ref, onMounted, nextTick } from 'vue'
import { LineOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { useStore } from '@/store'
import { useResizeObserver  } from '@vueuse/core'
import { useDrag } from '../views/hooks/useDrag'
import { formatSeconds } from '@/utils/index';
import ContextMenu from './ContextMenu.vue'
import feeService from '../api/fee'
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽终端
const headModalRef = ref();
const transformStyle = ref(useDrag(headModalRef));
/**表格 */
const columns = [
  // { title: '集群账户', dataIndex: 'account', key: 'account' },
  // { title: '用户名', dataIndex: 'userName', key: 'userName' },
  { title: '作业号', dataIndex: 'jobId',  key: 'jobId'},
  { title: '作业名', dataIndex: 'jobName', key: 'jobName' },
  { title: '集群', dataIndex: 'clusterName', key: 'clusterName'},
  { title: '集群分组', dataIndex: 'groupNameDesc', key: 'groupNameDesc'},
  { title: '分区', dataIndex: 'partitions', key: 'partitions'},
  { title: '节点', dataIndex: 'node', key: 'node'},
  { title: '核数', dataIndex: 'cpu', key: 'cpu'},
  { title: '作业开始运行时间', dataIndex: 'startTime', key: 'startTime'},
  { title: '作业结束运行时间', dataIndex: 'endTime', key: 'endTime'},
  { title: '运行时长', dataIndex: 'consumeTime', key: 'consumeTime'},
  { title: '消费核时', dataIndex: 'coreTime', key: 'coreTime'},
  // { title: '消费金额', dataIndex: 'price', key: 'price'},
  { title: '账单创建时间', dataIndex: 'createTime', key: 'createTime'},
];
/**搜索 */
const dateArr = ref([]);
const searchData = ref<any>({
  jobName: '',
  pageNo: 1,
  pageSize: 20,
});
const fileboxRef = ref();
const tableScrolly = ref(400);
onMounted(async () => {
  store.working.push('charging');
  await getList();
  nextTick(() => {
    useResizeObserver(fileboxRef.value, (entries) => { //监听终端窗口
      if(!fileboxRef.value) return;
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - 160;
    });
  })
});

const loading = ref(false);
const dataList = ref<any>([]);
const total = ref(0);
/**获取文件管理 */
async function getList() {
  loading.value = true;
  const { data } = await feeService.getFeeList(store.authInfo.tenantCode, searchData.value as any);
  dataList.value = data.list;
  total.value = data.total;
  loading.value = false;
}

/**日期筛选 */
function dateChange() {
  if (dateArr.value) {
    searchData.value.startTime = dateArr.value[0] + ' 00:00:00';
    searchData.value.endTime = dateArr.value[1] + ' 23:59:59';
  } else {
    dateArr.value = [];
    searchData.value.startTime = ''
    searchData.value.endTime = ''
  }
  getList();
}

/**右键菜单 */
const isShowMenu = ref(false);
const menuPosition = ref({x: 0, y: 0});
const menus = ref(['refresh']);
function showMenu(event: any) {
  menuPosition.value.x = event.clientX;
  menuPosition.value.y = event.clientY;
  isShowMenu.value = true;
  event.stopPropagation();
}

/**右键菜单 */
function menuChange({ menuType }: any) {
  switch(menuType) {
    case 'refresh' : getList(); break;
  };
  isShowMenu.value = false;
}

/**页改变 */
function handlePageChange(page: number) {
  searchData.value.pageNo = page;
  getList();
}

/**页数改变 */
function handleSizeChange(current: number, size: number) {
  searchData.value.pageNo = current;
  searchData.value.pageSize = size;
  getList();
}

/**打开文件管理 */
async function open() {
  showModal.value = true;
}

/**隐藏 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('charging'), 1);
  emit('close')
};
defineExpose({open, close})
</script>

<style scoped lang="scss" >
@import url(./antdmodal.scss);
.container {
  width: 100%;
  margin-top: 12px;
  height: 100%;
  .card-head {
    padding: 0 20px;
  }
  :deep(.ant-page-header) {
    padding: 8px 24px;
  }
  .card {
    padding: 20px;
    :deep(.ant-table-tbody>tr>td) {
      padding-top: 4px;
      padding-bottom: 4px;
    }
    .file-name {
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
</style>
