<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('webEdit')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal">
    <div class="container">
      <a-tabs class="tabs" v-model:activeKey="activeKey" type="editable-card" :hideAdd="true" size="small" @edit="onTabEdit" @change="tabchange">
        <a-tab-pane v-for="(pane, index) in tabList" :key="pane.pwd" :tab="pane.text" :closable="index != 0" />
      </a-tabs>
      <div class="editBox" ref="editRef"></div>
    </div>
    <template #title>
      <div class="modal-head" ref="modalRef">
        <div class="title-text">编辑器</div>
        <!-- <LineOutlined class="small-icon" @click="hide" /> -->
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #footer>
      <div style="padding-bottom: 12px; padding-right: 12px;">
        <a-button size="small" @click="close">取消</a-button>
        <a-button size="small" type="primary" :loading="saveLoading" @click="saveFile">保存</a-button>
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('webEdit')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue'
import { useDrag } from '../views/hooks/useDrag'
import { useStore } from '@/store';
import * as monaco from 'monaco-editor';
import fileService from '../api/file'
import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker";
import { message } from 'ant-design-vue';
import { v4 as uuidv4 } from 'uuid';

const props = defineProps<{ levelList: string[], changeLevelIndex:Function, itemInfo: any }>();
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const modalRef = ref();
const transformStyle = ref(useDrag(modalRef));

// 编辑器实例
let editor: any;
const editRef = ref();

// 导航列表
const activeKey = ref();
const fileMap = ref<Map<string, any>>(new Map());
const tabList = computed(() => Array.from(fileMap.value.values()))
watch(() => props.itemInfo, () => {
  fileMap.value.set(props.itemInfo.pwd, props.itemInfo)
  activeKey.value = props.itemInfo.pwd;
  if (editor) {
    editor.setValue('读取中');
    download(props.itemInfo.pwd);
  }
}, { immediate: true });

onMounted(() => {
  store.working.push('webEdit');
  createMonacoEditor();
});

/**编辑器初始化 */
function createMonacoEditor() {
  window.MonacoEnvironment = {
    getWorker(_, _label) {
      return new editorWorker();
    },
  };
  editor = monaco.editor.create(editRef.value, {
    value: '读取中...',
    // language: 'javascript',
    theme: 'vs-dark',
    automaticLayout: true
  });
  download(props.itemInfo.pwd);
}

/**删 */
function onTabEdit(targetKey: string) {
  fileMap.value.delete(targetKey);
  if (targetKey == activeKey.value) {
    const lastItem = tabList.value[tabList.value.length - 1]
    if (editor) {
      editor.setValue(downResultMap.value.get(lastItem.pwd));
      activeKey.value = lastItem.pwd;
    }
  }
}

/**切换编辑文件 */
function tabchange(value: string) {
  if (editor) {
    editor.setValue(downResultMap.value.get(value));
  }
}

const downResultMap = ref(new Map())
/**下载文件 */
async function download(path: string) {
  try {
    const { data } = await fileService.preDownFile(store.authInfo.tenantCode, {filePath: path}, props.itemInfo.clusterInfo.id, props.itemInfo.clusterInfo.url);
    const fileParts = [];
    for (let i = 0; i < data; i++) { 
      const file = await fileService.downFile(store.authInfo.tenantCode, {filePath: path,  part: i+1}, props.itemInfo.clusterInfo.id, props.itemInfo.clusterInfo.url);
      fileParts.push(file);
    }
    const finalBlob = new Blob(fileParts);
    const fileReader= new FileReader();
    fileReader.readAsText(finalBlob,'utf-8')
    fileReader.onload = function(e: any) {  
      // 读取完成，result 属性包含文件内容
      editor.setValue(e.target.result);
      editor.getModel()?.setEOL(monaco.editor.EndOfLineSequence.LF);
      downResultMap.value.set(path, e.target.result)
    };
  } catch (error) {
    console.log(error)
  }
};

const saveLoading = ref(false);
/**保存文件 */
async function saveFile() {
  const item = fileMap.value.get(activeKey.value);
  const blob = new Blob([editor.getValue()], {type: 'application/json'});
  const newFile = new File([blob], item.text, { type: 'application/json' });
  const data: {[key: string]: any} = {
    currentPath: item.directory,
    isFragment: newFile.size > 50000000 ? true : false, // 大于50MB进行分片
    fileName: item.text,
    uniqueValue: uuidv4(),
    part: 1,
    totalPart: 1,
    filePath: '',
    file: newFile
  }
  const formData = new FormData();
  for(let key in data) {
    formData.append(key, data[key]);
  }
  try {
    saveLoading.value = true;
    downResultMap.value.set(item.pwd, editor.getValue())
    await fileService.uploadFile(store.authInfo.tenantCode, formData, props.itemInfo.clusterInfo.id, props.itemInfo.clusterInfo.url);
    // emit('close');
    saveLoading.value = false;
    message.success('保存成功')
  } catch (error) {
    saveLoading.value = false;
  }
}

/**打开 */
function open() {
  showModal.value = true;
}

/**隐藏 */
// function hide() {
//   showModal.value = false;
// }

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('webEdit'), 1);
  emit('close')
};

defineExpose({open, close})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  height: 100%;
  .editBox {
    height: calc(100% - 36px);
  }
  :deep(.ant-tabs-nav) {
    margin: 0;
  }
}
</style>
