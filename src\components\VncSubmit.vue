<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf(softwareInfo ? softwareInfo.id : 'vncSubmit')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '30%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null"
    >
    <div class="container" ref="containerRef" @contextmenu="contextmenu">
      <a-form layout="horizontal" ref="formDataRef" :model="formData">
        <a-form-item  label="集群分区" name="connectId" :rules="[{ required: true, message: '请输入集群分区' }]">
          {{  clusterInfo.clusterName + "-" + clusterInfo.groupNameDesc }}
        </a-form-item>
        <a-form-item v-if="formData.connectId" label="核心数量" name="cpuNum" :rules="[{ validator: checkCpuNum }]">
          <a-input type="number" :min="1" :max="clusterInfo.coreNum" v-model:value="formData.cpuNum" :placeholder="`核心数(范围：1-${clusterInfo.coreNum || '-'})`" />
        </a-form-item>
      </a-form>
      <div class="agreement">
        <a-checkbox v-model:checked="agreementVnc">我已知晓</a-checkbox>
        <span>VNC 作业不会自动结束，未关闭期间将持续计费，需自行在作业管理中取消作业</span>
      </div>
      <a-button type="primary" style="width: 100%;" :loading="submitLoading" @click="submit">创建VNC作业</a-button>
      <div class="vnc-tips">请在作业管理中查看所创建的vnc作业任务</div>
    </div>
    <template #title>
      <div class="modal-head" ref="jobmodalRef">
        <div class="title-text">{{ softwareInfo?.name || '提交vnc'}}</div>
        <!-- <LineOutlined class="small-icon" @click="hide" /> -->
        <!-- <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt=""> -->
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex(softwareInfo ? softwareInfo.id : 'vncSubmit')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, computed } from 'vue'
// import { LineOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { useResizeObserver  } from '@vueuse/core'
// import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker";
// import * as monaco from 'monaco-editor';
import { useDrag } from '../views/hooks/useDrag'
import { message } from 'ant-design-vue';
import workService from '@/api/work'
import { useStore } from '@/store';
import { contextmenu } from '@/utils/index'
import { Modal } from 'ant-design-vue';
const props = defineProps<{ levelList: string[], changeLevelIndex:Function, softwareInfo?: any }>();
const emit = defineEmits(['close']);
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const jobmodalRef = ref();
const transformStyle = ref(useDrag(jobmodalRef));

const containerRef = ref();
const tableScrolly = ref(0);
// 是否同意
const agreementVnc = ref(false);

onMounted(async () => {
  store.working.push(props.softwareInfo ? props.softwareInfo.id : 'vncSubmit');
  nextTick(() => {
    useResizeObserver(containerRef.value, (entries) => { //监听终端窗口
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - 140;
    });
  });
})

/**基础信息 */
const formData = ref({
  current_working_directory: '',
  environment: props.softwareInfo?.environment,
  job_name: 'vnc',
  script: props.softwareInfo?.script || '',
  cpuNum: '',
  nodeNum: '1',
  clusterId: null as any,
  connectId: null as any,
  softwareId: props.softwareInfo?.id || ''
});

// 当前集群分区
const clusterInfo = computed(() => {
  const data = store.userInfo.userClusterInfo.find((item: any) => item.gidNumber == props.softwareInfo.gidNumber) || {};
  formData.value.connectId = data.id;
  formData.value.clusterId = data.clusterId;
  formData.value.current_working_directory = data.currentDirectory;
  return data;
})

/*校验核心数 */
const checkCpuNum = async (_rule: any, value: number) => {
  if (!value) {
    return Promise.reject('请输入核心数');
  }
  if (value <= 0) {
    return Promise.reject('请输入核心数大于0');
  } 
  if (/^[1-9]\d*$/.test(`${value}`) == false) {
    return Promise.reject('核心数不能是小数');
  } 
  return Promise.resolve();
};

const formDataRef = ref();
const submitLoading = ref(false);
/**提交作业 */
async function submit() {
  try {
    await formDataRef.value.validate()
    submitLoading.value = true;
    if (formData.value.script.trim() == '') {
      submitLoading.value = false;
      return message.warning('请输入执行命令！');
    }
    if (!agreementVnc.value) {
      submitLoading.value = false;
      return message.warning('请勾选我已知晓');
    }
    if (clusterInfo.value.coreNum && Number(formData.value.cpuNum) > Number(clusterInfo.value.coreNum)) {
      submitLoading.value = false;
      return message.warning('请输入范围之内的核心数');
    }
    // 提交集群分区是否欠费
    await checkCoreTime();
    // 提交
    const { data } = await workService.createdVncInit(store.authInfo.tenantCode, formData.value);
    if (!data) {
      submitLoading.value = false;
      return message.error('获取vnc地址错误！')
    };
    // 桌面应用跳转浏览器
    if(window.electronAPI) {
      window.electronAPI.sendMessageToMain({ type: 'download', url: data  });
    } else {
      window.open(data, '_blank');
    }
    submitLoading.value = false;
    close();
  } catch (error) {
    submitLoading.value = false;
  }
}

/**重置 */
function restForm() {
  formData.value = {
    current_working_directory: '',
    environment: [],
    job_name: '',
    script: '',
    cpuNum: '',
    nodeNum: '',
    clusterId: null,
    connectId: null,
    softwareId: ''
    // partition: null
  }
}

const errorTips = ref<{[key: string]: boolean}>({})
/**核时监听-是否欠费 */
function checkCoreTime() {
  return new Promise((resolve, reject) => {
    const clusterInfo = store.userInfo.userClusterInfo.find((item: { id: string; }) => item.id == formData.value.connectId);
    if(clusterInfo) {
      // 主账户/子账户剩余核时
      const usedCoreTime = store.userInfo.roleType == 2 ? Number(clusterInfo.residueCoreTime) : Number(clusterInfo.parentCanUsedCoreTime)
      if (usedCoreTime <= 0 && !errorTips.value[clusterInfo.id]) {
        Modal.error({
          class: 'remind-modal',
          title: '核时欠费',
          content: '您当前提交的集群分区余额核时不足，无法提交作业，请及时联系管理员充值',
          onOk: () => {
            delete errorTips.value[clusterInfo.id];
          },
          getContainer: () => containerRef.value
        });
        errorTips.value[clusterInfo.id] = true
        return reject(false);
      }
      resolve(true);
    };
  })
}

/**打开弹窗 */
async function open() {
  showModal.value = true;
}

/**隐藏弹窗 */
// function hide() {
//   showModal.value = false;
// }

/**关闭弹窗 */
function close() {
  showModal.value = false;
  restForm();
  store.working.splice(store.working.indexOf(props.softwareInfo ? props.softwareInfo.id : 'vncSubmit'), 1);
  emit('close');
};

defineExpose({open, close, name: props.softwareInfo ? props.softwareInfo.id : 'vncSubmit'})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  width: 100%;
  height: 100%;
  padding: 24px;
  overflow-y: scroll;
  position: relative;
}
.vnc-tips {
  text-align: center;
  margin-top: 12px;
  color: #63a9d8;
}
.agreement {
  text-align: center;
  color: #ff0000;
  font-weight: bold;
  margin-bottom: 12px;
}
// 核时提醒
:deep(.remind-modal .ant-modal-content){
  padding: 20px 24px!important;
  font-weight: 500;
}
:deep(.remind-modal .ant-modal-header){
  border-bottom: none;
}
</style>
