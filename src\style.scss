p {
  padding: 0;
  margin: 0;
}
// modal对话弹窗放大
.full-modal{
  .ant-modal {
    max-width: 100%;
    top: 0!important;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
  }
}
// 通知样式修改
.ant-notification-notice {
  padding: 6px 24px!important;
}
.ant-notification-notice-close {
  top: 10px!important;
}
.ant-notification-notice-message {
  margin-bottom: 4px!important;
  font-size: 15px!important;
}
/**单行文本溢出显示省略号*/
.text_ov1 {
  /*强制文本在一行内显示*/
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: auto;
}

/**多行文本溢出显示省略号*/
@for $i from 2 through 5 {
  .text_ov#{$i} {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $i;
    -webkit-box-orient: vertical;
    height: auto;
  }
}

// 整个滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
// 滚动条上的滚动滑块
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #90939955;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #90939977;
}
::-webkit-scrollbar-thumb:active {
  background-color: #90939999;
}
// 当同时有垂直滚动条和水平滚动条时交汇的部分
::-webkit-scrollbar-corner {
  background-color: transparent;
}