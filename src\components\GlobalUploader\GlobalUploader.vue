<template>
  <div id="global-uploader" :class="{ 'global-uploader-single': !global }">
    <!-- 上传 -->
    <uploader ref="uploaderRef" class="uploader-app" 
      :options="initOptions" 
      :file-status-text="fileStatusText"
      :auto-start="false" 
      @file-added="onFileAdded"
      @file-success="onFileSuccess" 
      @file-progress="onFileProgress"
      @file-error="onFileError">
      <uploader-unsupport></uploader-unsupport>

      <uploader-btn id="global-uploader-btn" ref="uploadBtnRef">选择文件</uploader-btn>

      <uploader-btn id="global-uploader-folder" :directory="true" ref="uploadFolderRef">选择目录</uploader-btn>
      
      <uploader-list v-show="panelShow">
        <template #default="{ fileList }">
          <div class="file-panel" :class="{ collapse: collapse }">
            <div class="file-title">
              <div class="title">文件列表</div>
              <div class="operate">
                <a-button :title="collapse ? '展开' : '折叠'" type="link" @click="collapse = !collapse">
                  <FullscreenOutlined v-if="collapse" />
                  <MinusOutlined v-else />
                </a-button>
                <a-button title="关闭" type="link" size="small" @click="close">
                  <CloseCircleOutlined />
                </a-button>
              </div>
            </div>

            <ul class="file-list">
              <li v-for="file in fileList" :key="file.id" class="file-item">
                <uploader-file ref="files" :class="['file_' + file.id, customStatus]" :progress="0.2" :file="file" :list="true"></uploader-file>
              </li>
              <div v-if="!fileList.length" class="no-file">暂无待上传文件</div>
            </ul>
          </div>
        </template>
      </uploader-list>
    </uploader>
  </div>
</template>

<script lang="ts">
/**
 *  全局上传插件，两种调用方式
 *   1. 作为全局页面的组件，使用event bus
 *   调用方法：Bus.emit('openUploader', {params: {}, options: {}})
 *               params: 发送给服务器的额外参数；
 *               options：上传选项，目前支持 target、testChunks、mergeFn、accept
 *
 *   监听函数：Bus.on('fileAdded', fn); 文件选择后的回调
 *           Bus.on('fileSuccess', fn); 文件上传成功的回调，监听后记得释放
 *
 *   2. 作为普通组件在单个页面中调用，使用props
 */
import { ref, watch, computed, nextTick, onMounted } from 'vue'
import { v4 as uuidv4 } from 'uuid';
import Bus from './utils/bus'
import fileService from '@/api/file'
import { validateFolderName } from '../../utils/index'
import { CloseCircleOutlined, FullscreenOutlined, MinusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useThrottleFn } from '@vueuse/core'
import { useStore } from '@/store';
const store = useStore();
const throttleFn = useThrottleFn((callBackFn) => {if(callBackFn) callBackFn()}, 1000);
export default {
  name: 'GlobalUploader',
  components: {
    CloseCircleOutlined,
    FullscreenOutlined,
    MinusOutlined
  },
  props: {
    global: {
      type: Boolean,
      default: true
    },
    // 发送给服务器的额外参数
    params: {
      type: Object
    },
    options: {
      type: Object,
    },
    dataList: {
      type: Array,
      default: []
    },
    clusterInfo: {
      type: Object,
      default: {}
    }
  },
  emits: ['fileAdded', 'fileSuccess'],

  setup(props, { emit }) {
    const env = import.meta.env;
    const baseURL = ref(props.clusterInfo?.url) // env.VITE_APP_UPLOAD_URL;
    const proxyUrl = env.VITE_APP_PROXY_PREFIX_UP;
    const initOptions = {
      uploadMethod: 'POST',
      target: `${env.DEV ? proxyUrl : baseURL.value }/download/upload`,
      chunkSize: '50000000',
      fileParameterName: 'file',
      maxChunkRetries: 1,
      simultaneousUploads: 1,
      // 是否开启服务器分片校验
      testChunks: false,
      // 跳过该文件
      allowDuplicateUploads: true,
      headers: (file: any) => {
        return { 
          token: file.uploadToken, 
          connectid: props.clusterInfo?.id 
        }
      },
      initFileFn: (file: any) => {
        file.unid = uuidv4();
      },
      // initialPaused: false,
      processParams: (params: any, _file: any, _chunk: any) => {
        return {
          chunkIndex: params.chunkNumber - 1 || 0,
          totalChunks: params.totalChunks,
        }
      },
      processResponse: async (response: any, cb: Function, file: any) => {
        try {
          const result = JSON.parse(response);
          if (file.progress() < 1) {
            cb(null, result);
            return;
          }
          if (result.data) {
            // 合并文件
            await fileService.endUploadMerge(props.clusterInfo?.id, props.clusterInfo?.url, file.uploadToken);
            cb(null, response);// 返回解析后的对象
          }
          cb(null, response);// 返回解析后的对象
        } catch (err) {
          cb(err); // 解析失败时返回错误
        }
      },
    }
    const fileStatusText = {
      success: '上传成功',
      error: '上传失败',
      uploading: '上传中',
      paused: '已暂停',
      waiting: '等待上传'
    }
    const customStatus = ref('')
    const panelShow = ref(false)
    const collapse = ref(false)
    const uploaderRef = ref()
    const uploadBtnRef = ref()
    const uploadFolderRef = ref()

    const uploader = computed(() => uploaderRef.value?.uploader)

    let customParams = {}
    watch(
      () => props.params,
      (data) => {
        if (data) {
          customParams = data
        }
      }
    )
    watch(
      () => props.options,
      (data) => {
        if (data) {
          setTimeout(() => {
            customizeOptions(data)
          }, 0)
        }
      }
    )

    watch(() => props.clusterInfo, () => {
      baseURL.value = props.clusterInfo?.url
      uploader.value.opts.target = `${env.DEV ? proxyUrl : baseURL.value }/download/upload`;
      uploader.value.opts.headers.connectid = props.clusterInfo?.id;
    })
    // 自定义options
    function customizeOptions(opts: any) {
      // 自定义上传url
      if (opts.target) {
        uploader.value.opts.target = opts.target
      }
      // 是否可以秒传、断点续传
      if (opts.testChunks !== undefined) {
        uploader.value.opts.testChunks = opts.testChunks
      }
      // 自定义文件上传类型
      if (opts.accept) {
        let input = document.querySelector('#global-uploader-btn input')
        input?.setAttribute(opts.accept, opts.accept.join())
      }
    }

    /**
     * 添加文件
     * @param file 
     */
    async function onFileAdded(file: any) {
      panelShow.value = true;
      trigger('fileAdded');
      // 额外的参数
      file.params = customParams; 
      // file.unid = uuidv4();
      // 获取文件token
      const pathList = file.relativePath.split('/');
      const filePath = pathList.length > 1 ? file.relativePath.replace(`/${pathList.pop()}`, '') : '';
      const { data } = await fileService.getUploadToken(store.authInfo.tenantCode, props.clusterInfo?.id, props.clusterInfo?.url, {
        path: file.params.currentPath,
        fileName: file.name,
        chunkSum: file.chunks.length,
        filePath: filePath,
      });
      // uploader.value.opts.headers.token = data;
      file.uploadToken = data;
      setTimeout(() => { file.resume() }, 500)
    }

    /**
     * 上传成功
     * @param _rootFile 
     * @param file 
     * @param response 
     * @param _chunk 
     */
    async function onFileSuccess(rootFile: any, _file: any, response: any, _chunk: any) {
      let res = JSON.parse(response)
      // 服务端自定义的错误（即http状态码为200，但是是错误的情况），这种错误是Uploader无法拦截的
      if (res.code !== 200) {
        error(res.message)
        // 文件状态设为“失败”
        uploader.value.pause();
        statusSet(rootFile.id, 'failed')
        return
      };
      trigger('fileSuccess');
    }

    /**
     * 新增的自定义的状态: 'md5'、'merging'、'transcoding'、'failed'
     * @param id
     * @param status
     */
    function statusSet(id: string, status: string) {
      const statusMap: any = {
        transcoding: {
          text: '校验中',
          bgc: '#fff'
        },
        failed: {
          text: '上传失败',
          bgc: '#fff',
          color: '#f04134'
        }
      }

      customStatus.value = status
      nextTick(() => {
        const statusTag = document.createElement('p')
        statusTag.className = `custom-status-${id} custom-status`
        statusTag.innerText = statusMap[status].text
        statusTag.style.backgroundColor = statusMap[status].bgc
        statusTag.style.color = statusMap[status].color
        const statusWrap = document.querySelector(`.file_${id} .uploader-file-status`)
        statusWrap?.appendChild(statusTag)
      })
    }

    function onFileProgress(_rootFile: any, _file: any, _chunk: any) {
      // console.log(
      //   `上传中 ${file.name}，chunk：${chunk.startByte / 1024 / 1024} ~ ${
      //     chunk.endByte / 1024 / 1024
      //   }`
      // )
    }
    function onFileError(_rootFile: any, _file: any, response: any, _chunk: any) {
      error(response)
    }
    function close() {
      // if (uploader.value.isUploading()) {
      //   return message.warning('正在上传文件')
      // }
      // uploader.value.cancel()
      panelShow.value = false
    }

    function trigger(e: any) {
      Bus.emit(e)
      emit(e)
    }
    function error(msg: string) {
      message.error(msg)
    }

    onMounted(() => {
      uploader.value.on('change', (event: any) => {
        const files: any[] = Array.from(event.target.files);
        // 上传列表中是否存在上传的相同文件夹
        const folder = files[0]?.webkitRelativePath;
        if (folder) {
          const folderName = folder.split('/')[0];
          for (let item of uploader.value.fileList) {
            if (folderName == item.name && item.completed) {
              uploader.value.removeFile(item);
            }
          }
        }
      })

      uploader.value.on('fileAdded', (file: any) => {
        const rootFile = file.getRoot();
        // 上传校验特殊字符文件路径
        const checkName = validateFolderName(rootFile.name);
        if (!checkName.isValid) {
          throttleFn(() => message.warning(checkName.message));
          return false
        };
        // 校验重复文件
        if (props.dataList.some((item: any) => (rootFile.name == item.text) )) {
          throttleFn(() => message.warning('已存在文件或目录！'))
          return false
        };
      })

      Bus.on('openUploader', (data: any) => {
        customParams = data.params
        customizeOptions(data.options || {})
        if (uploadBtnRef.value) {
          uploadBtnRef.value.$el.click()
        }
      })

      Bus.on('openUploaderFolder', (data: any) => {
        customParams = data.params
        customizeOptions(data.options || {})
        if (uploadFolderRef.value) {
          uploadFolderRef.value.$el.click()
        }
      })
    });

    return {
      initOptions,
      fileStatusText,
      customStatus,
      panelShow,
      collapse,
      uploaderRef,
      uploader,
      uploadBtnRef,
      uploadFolderRef,
      onFileAdded,
      onFileSuccess,
      onFileProgress,
      onFileError,
      close
    }
  }
}
</script>

<style lang="scss">
ul {
  padding: 0;
}
li {
  list-style: none;
}
// 定义项目全局变量
$blue: #108ee9;
$red: #f04134;
$green: #00a854;
$orange: #f56a00;
$yellow: #ffbf00;
$pink: #f5317f;
$purple: #7265e6;
$cyan: #00a2ae;
$grey: #bfbfbf;
#global-uploader {
  &:not(.global-uploader-single) {
    position: fixed;
    z-index: 20;
    right: 15px;
    bottom: 15px;
    box-sizing: border-box;
  }

  .uploader-app {
    width: 540px;
  }

  .file-panel {
    background-color: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 7px 7px 0 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);

    .file-title {
      display: flex;
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
      border-bottom: 1px solid #ddd;

      .operate {
        flex: 1;
        text-align: right;

        .el-button {
          --el-button-hover-link-text-color: #{$blue};

          + .el-button {
            margin-left: 8px;
          }
        }
      }
    }

    .file-list {
      position: relative;
      height: 240px;
      overflow-x: hidden;
      overflow-y: auto;
      background-color: #fff;
      transition: all 0.3s;

      .file-item {
        background-color: #fff;
      }
    }

    &.collapse {
      .file-title {
        background-color: #e7ecf2;
      }
      .file-list {
        height: 0;
      }
    }
  }

  .no-file {
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;

    svg {
      vertical-align: text-bottom;
    }
  }

  .uploader-file-icon {
    &[icon='image']:before {
      content: '' !important;
    }
    &[icon='audio']:before {
      content: '' !important;
    }
    &[icon='video']:before {
      content: '' !important;
    }
    &[icon='document']:before {
      content: '' !important;
    }

    &[icon='image'] {
      background: url(./images/image-icon.png)!important;
    }
    &[icon='audio'] {
      background: url(./images/audio-icon.png);
      background-size: contain;
    }
    &[icon='video'] {
      background: url(./images/video-icon.png);
    }
    &[icon='document'] {
      background: url(./images/text-icon.png);
    }
    &[icon='unknown'] {
      background: url(./images/zip.png) no-repeat center;
      background-size: contain;
    }
  }

  .uploader-file-name {
    width: 40%;
  }
  .uploader-file-meta {
    display: none;
  }
  .uploader-file-status {
    width: 37%;
  }
  .uploader-file-actions > span {
    margin-right: 6px;
  }

  .custom-status {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }
}

/* 隐藏上传按钮 */
#global-uploader-btn {
  position: absolute;
  clip: rect(0, 0, 0, 0);
}
#global-uploader-folder {
  position: absolute;
  clip: rect(0, 0, 0, 0);
}

.global-uploader-single {
  #global-uploader-btn {
    position: relative;
  }
}
</style>
