import { type Directive } from "vue";

export const resize: Directive = {
  mounted(el, binding) {  
    const edges = {  
      left: false,  
      right: false,  
      top: false,  
      bottom: false,  
    };
    let startX: number, startY: number, initialWidth: number, initialHeight: number, activeEdge: string | null;  
  
    // 设置鼠标图标样式  
    const setCursorStyle = () => {
      el.style.resize = 'none';
      el.style.overflow = 'hidden'
      switch(activeEdge) {
        case 'top' :
        case 'bottom' :  el.style.cursor = 'row-resize';  break; // 垂直缩放光标
        case 'left' :
        case 'right' :  el.style.cursor = 'col-resize'; break; // 水平缩放光标  
        default: el.style.cursor = ''; // 默认光标
      }
    };

    // 检查鼠标是否位于窗口边缘  
    const checkEdge = (e: { clientX: any; clientY: any; }) => {  
      const rect = el.getBoundingClientRect();  
      const { clientX: x, clientY: y } = e;  
      const edgeSize = 30; // 边缘敏感区域大小  
      
      edges.left = x >= rect.left && x <= rect.left + edgeSize;  
      edges.right = x >= rect.right - edgeSize && x <= rect.right;  
      edges.top = y >= rect.top && y <= rect.top + edgeSize;  
      edges.bottom = y >= rect.bottom - edgeSize && y <= rect.bottom;  
  
      // 根据鼠标位置设置活动的边缘  
      activeEdge = (edges.left && 'left') || (edges.right && 'right') || (edges.top && 'top') || (edges.bottom && 'bottom') || null;  

      setCursorStyle();  
    };

    const handleResize = (e: { clientX: number; clientY: number; }) => {
      // if(!activeEdge) return;
      const deltaX = e.clientX - startX;  
      const deltaY =  startY - e.clientY ;  
      // 计算新的宽度和高度，确保它们不会小于某个最小值  
      const newWidth = Math.max(initialWidth + deltaX, 100);  
      const newHeight = Math.max(initialHeight + deltaY, 100);  
  
      if(binding.value) { // 有值
        switch(binding.value) {
          case 'top' :
          case 'bottom' :  el.style.height = `${newHeight}px`;  break;
          case 'left' :
          case 'right' :  el.style.width = `${newWidth}px`; break;
        }
        return;
      }
      el.style.width = `${newWidth}px`;  
      el.style.height = `${newHeight}px`;  
    };

    const handleMouseDown = (e: { clientX: number; clientY: number; preventDefault: () => void; }) => {  
      // 记录鼠标按下时的位置和元素的初始尺寸  
      startX = e.clientX;  
      startY = e.clientY;  
      initialWidth = el.offsetWidth;  
      initialHeight = el.offsetHeight;  
  
      // 添加鼠标移动和鼠标释放事件监听器  
      document.addEventListener('mousemove', handleResize);  
      document.addEventListener('mouseup', handleMouseUp);  
  
      // 阻止默认行为（例如文本选择）  
      e.preventDefault();  
    };  
  
    // 处理鼠标释放事件  
    const handleMouseUp = () => {  
      activeEdge = null;  
      document.removeEventListener('mousemove', handleResize);  
      document.removeEventListener('mouseup', handleMouseUp);  
    };  
  
    // 监听鼠标移动事件以更新光标样式  
    el.addEventListener('mousemove', checkEdge);  
  
    // 添加鼠标按下事件监听器到元素上  
    el.addEventListener('mousedown', handleMouseDown);  
  
    // 为了在指令解绑时能够正确移除事件监听器，我们需要将它们存储在元素上  
    el.__vue_resize_handleMouseDown__ = handleMouseDown;  
    el.__vue_resize_handleResize__ = handleResize;  
    el.__vue_resize_handleMouseUp__ = handleMouseUp;  
  },  
  unmounted(el) {  
    // 移除鼠标按下事件监听器，并清理其他可能的事件监听器  
    el.removeEventListener('mousedown', el.__vue_resize_handleMouseDown__);  
    document.removeEventListener('mousemove', el.__vue_resize_handleResize__);  
    document.removeEventListener('mouseup', el.__vue_resize_handleMouseUp__);  
  
    // 清理存储在元素上的引用  
    delete el.__vue_resize_handleMouseDown__;  
    delete el.__vue_resize_handleResize__;  
    delete el.__vue_resize_handleMouseUp__;  
  },
}