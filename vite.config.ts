import { defineConfig, loadEnv } from 'vite'
import fs from 'node:fs'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import electron from 'vite-plugin-electron/simple'
import pkg from './package.json'

export default defineConfig(({ mode, command }: any) => {
  const env = loadEnv(mode, process.cwd())
  const proxyPrefix = env.VITE_APP_PROXY_PREFIX
  const proxyPrefixUp = env.VITE_APP_PROXY_PREFIX_UP

  fs.rmSync('dist-electron', { recursive: true, force: true })
  const isElectron = env.VITE_APP_DESKTOP == 'electron';
  const isServe = command === 'serve'
  const isBuild = command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG
  return {
    plugins: [
      vue(),
      isElectron ? electron({
        main: {
          // Shortcut of `build.lib.entry`
          entry: 'electron/main/index.ts',
          onstart({ startup }) {
            if (process.env.VSCODE_DEBUG) {
              console.log(/* For `.vscode/.debug.script.mjs` */'[startup] Electron App')
            } else {
              startup()
            }
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
              },
            },
          },
        },
        preload: {
          input: 'electron/preload/index.ts',
          vite: {
            build: {
              sourcemap: sourcemap ? 'inline' : undefined, // #332
              minify: isBuild,
              outDir: 'dist-electron/preload',
              rollupOptions: {
                external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
              },
            },
          },
        },
        renderer: {},
      }): null,
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    server: {
      host: '0.0.0.0',
      port: 4000,
      proxy: {
        [proxyPrefix]: {
          target: env.VITE_APP_BASE_URL,
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: path => path.replace(new RegExp(`^${proxyPrefix}`), ''),
        },
        [proxyPrefixUp]: {
          target: env.VITE_APP_UPLOAD_URL,
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: path => path.replace(new RegExp(`^${proxyPrefixUp}`), ''),
        },
      },
    },
    clearScreen: false,
  }
})


