{"name": "cloud-desktop", "private": true, "version": "1.0.0", "main": "dist-electron/main/index.js", "type": "module", "description": "调度平台", "author": {"name": "天玑智算", "email": "<EMAIL>"}, "scripts": {"dev": "vite serve --mode development", "build:dev": "vue-tsc && vite build --mode development", "build:pre": "vue-tsc && vite build --mode pre", "build:prod": "vue-tsc && vite build --mode production", "build": "vue-tsc && vite build", "build:windows": "vue-tsc --noEmit && vite build && electron-builder", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^10.10.0", "@xterm/addon-attach": "^0.11.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "ant-design-vue": "^4.2.1", "axios": "^1.7.7", "axios-retry": "^4.5.0", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "monaco-editor": "^0.52.0", "pinia": "^2.1.7", "qs": "^6.12.1", "uuid": "^11.0.2", "vue": "3.5", "vue-draggable-resizable": "^3.0.0", "vue-router": "^4.3.2", "vue-simple-uploader": "^1.0.3"}, "devDependencies": {"@types/lodash": "^4.17.4", "@types/node": "^20.12.12", "@types/qs": "^6.9.15", "@vitejs/plugin-vue": "^5.0.4", "electron": "^29.4.6", "electron-builder": "^24.13.3", "less": "^4.2.0", "sass": "^1.77.2", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-electron": "^0.28.4", "vite-plugin-electron-renderer": "^0.14.5", "vue-tsc": "^2.1.4"}}