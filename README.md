# 天玑智算调度平台 - 前端

## 说明

天玑智算调度平台基于 Vue3、TypeScript、Vite 和 Ant Design Vue。

## 快速开始

### 1. 安装

克隆仓库

```bash
<NAME_EMAIL>:phadcloud/cloud-desktop.git
```

安装依赖

```bash
cd cloud-desktop
npm i --registry https://mirrors.huaweicloud.com/repository/npm/
```

启动

```bash
npm run dev
```

构建

```bash
npm run build
```

## 项目结构

TODO @zhutongzhang

## 其他

### Git 提交 message 规范

| Emoji | 类型 | 描述 |
|-------|------|-------------|
| ✨ | feature/feat | 新功能（feature/feat） |
| 🐛 | fix | 修补 bug |
| 📚 | docs | 文档（documentation） |
| 💎 | style | 格式（不影响代码运行的变动，空格，格式化，等等） |
| 📦 | refactor | 重构（对原有功能的重新修改或项目结构变动） |
| ⚡ | perf | 性能 （提高代码性能的改变） |
| ✅ | test | 增加测试或者修改测试 |
| 🛠️ | build | 影响构建系统或外部依赖项的更改 (maven,gradle,npm 等等） |
| 🔧 | ci | 对 CI 配置文件和脚本的更改 |
| 🗑️ | chore | 对非 src 和 test 目录的修改 |
| ⏪ | revert | Revert a commit |
| 🔒 | security | 安全相关的改动 |
| 🚀 | deploy | 部署相关的改动 |
| 🎨 | ui | 用户界面和用户体验相关的改动 |
| 📝 | typo | 修复拼写错误 |
| 🔥 | remove | 删除代码或文件 |
| 🚧 | wip | 工作进行中（Work In Progress） |
| 🔖 | version | 版本标签 |
| 💡 | comment | 添加或更新注释 |
