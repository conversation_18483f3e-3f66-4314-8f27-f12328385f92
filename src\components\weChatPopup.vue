<template>
  <a-modal
    style="top: 20%;"
    :zIndex="999"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="true"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '600px'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '400px' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container">
      <div class="qr-code">
        <h1>微信扫码关注公众号</h1>
        <a-result class="qr-result" status="success" v-if="scanned">
          <template #extra>
            <div style="cursor: pointer;" @click="refreshQr"><ReloadOutlined /> 刷新</div>
          </template>
        </a-result>
        <img width="200px" height="200px" :src="qrData?.qrUrl || ''"  alt="">
        <p class="qr-tips">友情提示：关注“天玑智算云”公众号，您将能够及时收到超算动态通知和某些作业报警消息，方便您更好的使用超算资源。</p>
      </div>
    </div>
    <template #modalRender="{ originVNode }">
      <component :is="originVNode" />
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import loginService from '@/api/login'
import { ReloadOutlined } from '@ant-design/icons-vue';
import { onMounted, onUnmounted, ref } from 'vue'
import { useStore } from '@/store';
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(false);
// 全屏
const isFullModal = ref(false);

onMounted(() => {
  getQrcode();
})

const qrData = ref();
/**获取公众号二维码 */
async function getQrcode() {
  try {
    const { data } = await loginService.getQrcode(store.userInfo.id);
    qrData.value = data;
    stopCheckFollow.value = false;
    if (!store.userInfo.isUnfollow) {
      checkFollow();
    }
    const img = new Image();
    img.src = data?.qrUrl;
    img.onload = () => {
      showModal.value = true;
    };
  } catch (error) {
    console.log(error)
  }
}

// 停止轮询查询
const stopCheckFollow = ref(false);
// 是否扫码成功
const scanned = ref(false);
/**查询关注状态 */
async function checkFollow(timeout = 5 * 60 * 1000) {
  const startTime = Date.now();
  // const maxRetryInterval = 30000; // 最大间隔30秒
  const initialInterval = 2000; // 初始间隔2秒
  let retryCount = 0;
  // let currentInterval = initialInterval;
  const poll = async () => {
    try {
      if (stopCheckFollow.value) return;
      retryCount++;
      // 检查超时
      if (Date.now() - startTime > timeout) {
        console.log('轮询超时（5分钟），停止查询');
        stopCheckFollow.value = true;
        getQrcode();
        return;
      }
      const { data } =  await loginService.getQrcodeStatus(qrData.value.sceneId);
      if (data.scanned) { // 扫码成功
        scanned.value = data.scanned;
      }
      if (data.isUnfollow) { // 已关注
        stopCheckFollow.value = true;
        close();
        return;
      }
      // 使用指数退避策略增加间隔时间
      // currentInterval = Math.min(
      //     initialInterval * Math.pow(2, retryCount - 1),
      //     maxRetryInterval
      // );
      // console.log(`下次查询将在${currentInterval/1000}秒后执行`);
      setTimeout(poll, initialInterval);
    } catch (error) {
      stopCheckFollow.value = true;
      console.error('轮询过程中出错:', error);
    }
  };
  poll();
}

/**刷新二维码 */
async function refreshQr() {
  stopCheckFollow.value = true;
  await getQrcode();
  scanned.value = false;
}

onUnmounted(() => {
  stopCheckFollow.value = true;
})

/**打开 */
function open() {
  showModal.value = true;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  emit('close')
};

defineExpose({open, close})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  height: 100%;

  .qr-code {
    text-align: center;
    position: relative;
    h1 {
      text-align: center;
      margin-bottom: 30px;
      color: #636363;
      font-style: italic;
    }
    .qr-tips {
      padding: 0 30px;
      margin: 30px 0;
      font-size: 16px;
      color: #62bae2;
    }
    .qr-result {
      width: 200px;
      height: 200px;
      box-sizing: border-box;
      margin: auto;
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #f3f3f3;
    }
  }
}
</style>
