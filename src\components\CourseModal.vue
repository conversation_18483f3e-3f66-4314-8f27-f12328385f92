<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('course')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container">
      <CourseList v-if="!isPlay" @change="change"></CourseList>
      <CourseVideo v-else :courseInfo="courseInfo" @back="(back: boolean) => isPlay = back "></CourseVideo>
    </div>
    <template #title>
      <div class="modal-head" ref="coursemodalRef">
        <div class="title-text">计算课堂</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('course')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { LineOutlined } from '@ant-design/icons-vue';
import CourseList from './course/CourseList.vue'
import CourseVideo from './course/CourseVideo.vue'
import { useDrag } from '../views/hooks/useDrag'
import { useStore } from '@/store';
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['close'])
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const coursemodalRef = ref();
const transformStyle = ref(useDrag(coursemodalRef));

onMounted(() => {
  store.working.push('course');
})

const isPlay = ref(false);
const courseInfo = ref();
function change(data: any) {
  isPlay.value = data.isPlay;
  courseInfo.value = data.courseInfo;
}

/**打开 */
function open() {
  showModal.value = true;
}

/**隐藏 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('course'), 1);
  emit('close')
};

defineExpose({open, close})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  height: 100%;
}
</style>
