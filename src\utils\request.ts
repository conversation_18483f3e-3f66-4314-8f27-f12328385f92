import axios from 'axios'
import { message } from 'ant-design-vue';
import {isEmpty} from 'lodash'
import * as qs from 'qs';
import { useThrottleFn } from '@vueuse/core'
import { useStore } from '@/store';
import axiosRetry from 'axios-retry';
import { fetchEventSource } from '@microsoft/fetch-event-source';
const throttleFn = useThrottleFn((callBackFn) => {if(callBackFn) callBackFn()}, 1000);
function createService () {
  // 创建一个 axios 实例
  const service = axios.create()
  // 重试机制
  axiosRetry(service, { retries: 2 , retryCondition: (error: any) => {
    if (error.config.baseURL == "/api") {
      return true
    }
    return false
  }})
  // HTTP request 拦截器
  service.interceptors.request.use(
    config => config,
    error => {
      // 失败
      return Promise.reject(error);
    }
  );
  // HTTP response 拦截器
  service.interceptors.response.use(
    response => {
      if ((response.headers['content-disposition'] || ! /^application\/json/.test(response.headers['content-type'])) && response.status === 200) {
        return response
      } else if (response.data.size) {
        // bold 数据
      } else if (response.data.code && response.data.code !== 200) {
        throttleFn(() =>     
          message.error({
            content: response.data.message || response.data.msg,
            // icon: () => h( IconFaceFrownFill )
          })
        )
        return Promise.reject(response.data);
      };
      return response.data;
    },
    async error => {
      const err = (text: string) => {
        throttleFn(() => 
          message.error({
            content: ( error.response && error.response.data && error.response.data.message )
            ? error.response.data.message == 'Invalid login' ? '登录过期' : error.response.data.message
            : text,
            // icon: () => h( IconFaceFrownFill )
          })
        )
      }
      if (error.response && error.response.data) {
        switch (error.response.status) {
          case 404:
            err('服务器资源不存在')
            break
          case 500:
            err('服务器内部错误')
            break
          case 401:
            err('登录过期')
            useStore().outLogin();
            // throttleFn( async () => {
            //   const token = await useStore().refreshToken();
            //   error.config.headers.token = token;
            //   service(error.config);
            // });
            break;
          case 403:
            err('没有权限访问该资源')
            break
          case 405:
            err('没有权限访问')
            break
          default:
            err('未知错误！')
        }
      } else {
        err('请求超时，服务器无响应！')
      }
      return Promise.reject(error.response && error.response.data ? error.response.data : null)
    }
  )
  return service
}

function stringify (data: any) {
  return qs.stringify(data, { allowDots: true, encode: false })
}

/**
 * @description 创建请求方法
 * @param {Object} service axios 实例
 */
function createRequest (service: any) {
  return function (config: any) {
    const env = import.meta.env;
    const baseURL = config.connect_url ? config.connect_url : env.VITE_APP_BASE_URL;
    const proxyUrl = config.connect_url ? env.VITE_APP_PROXY_PREFIX_UP : env.VITE_APP_PROXY_PREFIX;
    const tokenHeader = {'token': useStore().authInfo.token};
    const configDefault = {
      timeout: 1000 * 60,
      headers: {...tokenHeader, ...config.header},
      baseURL: env.DEV ? proxyUrl : baseURL,
      data: config.data,
    }
    const option = Object.assign(configDefault, config)
    // json
    if (!isEmpty(option.params)) {
      option.url = option.url + '?' + stringify(option.params)
      option.params = {}
    }

    return service(option)
  }
}

// 用于真实网络请求的实例和请求方法
export const service = createService()
export const request = createRequest(service)

/**创建SSE流式请求 */
export function createSSeRequest(options: {
  url: string, 
  header: any,
  onProgress: Function
}) {
  const env = import.meta.env;
  const baseURL = env.VITE_APP_BASE_URL + options.url;
  const proxyUrl = env.VITE_APP_PROXY_PREFIX + options.url;
  fetchEventSource(env.DEV ? proxyUrl : baseURL, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'token': useStore().authInfo.token,
      ...options.header
    },
    onmessage(event) {
      if (options.onProgress) options.onProgress(event.data)
    },
    onerror(err) {
      console.error('Error:', err);
    }
  });
}