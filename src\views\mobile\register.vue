<template>
  <div class="register-container" v-if="!registerSuccess">
    <div class="register-header">
      <div class="logo-area">
        <!-- <img src="@/assets/logo.png" alt="logo" class="logo" /> -->
      </div>
      <h2>欢迎加入天玑智算</h2>
      <p>开启您的智能计算之旅</p>
    </div>

    <a-form :model="formState" name="registerForm" @finish="onFinish" layout="vertical">
      <div class="form-content">
        <a-form-item name="nickName" :rules="[{ required: true, message: '请输入姓名' }]">
          <a-input v-model:value="formState.nickName" placeholder="请输入姓名" size="large">
            <template #prefix>
              <UserOutlined class="site-form-item-icon" />
            </template>
          </a-input>
        </a-form-item>
        <!-- <a-form-item name="userName" :rules="[{ required: true, message: '请输入账号' }]">
          <a-input v-model:value="formState.userName" placeholder="请输入账号" size="large">
            <template #prefix>
              <UserOutlined class="site-form-item-icon" />
            </template>
          </a-input>
        </a-form-item> -->
        <a-form-item name="phone" :rules="[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
          ]">
          <a-input v-model:value="formState.phone" placeholder="请输入手机号" size="large">
            <template #prefix>
              <PhoneOutlined class="site-form-item-icon" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="phoneCode" :rules="[{ required: true, message: '请输入手机验证码' }]">
          <a-row :gutter="16">
            <a-col :span="15">
              <a-input v-model:value="formState.phoneCode" placeholder="请输入手机验证码" size="large">
                <template #prefix>
                  <SafetyCertificateOutlined class="site-form-item-icon" />
                </template>
              </a-input>
            </a-col>
            <a-col :span="9">
              <a-button :disabled="phoneCountdown > 0" @click="sendPhoneCode" class="verify-code-btn" size="large" block>
                {{ phoneCountdown > 0 ? `${phoneCountdown}s` : '获取验证码' }}
              </a-button>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item name="email" :rules="[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]">
          <a-input v-model:value="formState.email" placeholder="请输入邮箱" size="large">
            <template #prefix>
              <MailOutlined class="site-form-item-icon" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item name="emailCode" :rules="[{ required: true, message: '请输入邮箱验证码' }]">
          <a-row :gutter="16">
            <a-col :span="15">
              <a-input v-model:value="formState.emailCode" placeholder="请输入邮箱验证码" size="large">
                <template #prefix>
                  <SafetyCertificateOutlined class="site-form-item-icon" />
                </template>
              </a-input>
            </a-col>
            <a-col :span="9">
              <a-button :disabled="emailCountdown > 0" @click="sendEmailCode" class="verify-code-btn" size="large" block>
                {{ emailCountdown > 0 ? `${emailCountdown}s` : '获取验证码' }}
              </a-button>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item name="pwd" :rules="[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码长度不能小于6位' }
          ]">
          <a-input-password v-model:value="formState.pwd" placeholder="请输入密码" size="large">
            <template #prefix>
              <LockOutlined class="site-form-item-icon" />
            </template>
          </a-input-password>
        </a-form-item>
        <!-- <a-form-item name="confirmPassword" :rules="[
            { required: true, message: '' },
            { validator: validateConfirmPassword }
          ]">
          <a-input-password v-model:value="formState.confirmPassword" placeholder="请再次输入密码" size="large">
            <template #prefix>
              <SafetyOutlined class="site-form-item-icon" />
            </template>
          </a-input-password>
        </a-form-item> -->
        <a-form-item name="unit" :rules="[{ required: true, message: '请输入单位、学校名称' }]">
          <a-input v-model:value="formState.unit" placeholder="请输入单位、学校名称" size="large">
            <template #prefix>
              <BankOutlined class="site-form-item-icon" />
            </template>
          </a-input>
        </a-form-item>

        <!-- <a-form-item name="inviteCode">
          <a-input :disabled="true" v-model:value="formState.inviteCode" placeholder="请输入邀请码（选填）" size="large">
            <template #prefix>
              <BarcodeOutlined class="site-form-item-icon" />
            </template>
          </a-input>
        </a-form-item> -->
        <div class="agreement-section">
          <a-checkbox v-model:checked="agreement" :class="{ 'agreement-error': !agreement }">
            我已阅读并同意<a @click.prevent="showAgreement">《用户协议》</a>和<a @click.prevent="showPrivacy">《隐私政策》</a>
          </a-checkbox>
        </div>

        <a-button type="primary" html-type="submit" :loading="loading" block class="submit-btn" size="large">
          立即注册
        </a-button>
      </div>
    </a-form>
  </div>
  <a-result  style="margin-top:100px;" v-if="registerSuccess" status="success" title="注册成功" sub-title="请登录天玑智算平台"></a-result>
  <UserAgreement v-model:visible="agreementVisible" type="agreement" />
  <UserAgreement v-model:visible="privacyVisible" type="privacy" />
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import accountService from '@/api/account'
import loginService from '@/api/login'
// import type { Rule } from 'ant-design-vue/es/form';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  LockOutlined,
  // BarcodeOutlined,
  BankOutlined,
  // GiftOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons-vue';
import UserAgreement from '../../components/UserAgreement.vue';
const route = useRoute();
const emit = defineEmits(['close']);
const loading = ref(false);
// 手机验证码计时
const phoneCountdown = ref(0);
// 邮箱验证码计时
const emailCountdown = ref(0);
const agreementVisible = ref(false);
const privacyVisible = ref(false);

// 注册成功
const registerSuccess = ref(false)

const agreement = ref(false)
const formState = reactive({
  nickName: '',
  userName: '',
  phone: '',
  phoneCode: '',
  email: '',
  emailCode: '',
  pwd: '',
  // confirmPassword: '',
  unit: '',
  registerInvitationCode: route.query.registerCode || '',
});

watch(() => formState.phone, () => {
  formState.userName = formState.phone;
})

// const validateConfirmPassword = async (_rule: Rule, value: string) => {
//   if (!value) {
//     return Promise.reject('请确认密码');
//   }
//   if (value !== formState.pwd) {
//     return Promise.reject('两次输入的密码不一致');
//   }
//   return Promise.resolve();
// };

/**发送手机验证码 */
const sendPhoneCode = async () => {
  if (/^1[3-9]\d{9}$/.test(formState.phone) == false) {
    message.warning('请输入有效的手机号')
    return;
  }
  phoneCountdown.value = 60;
  await accountService.sendPhoneCode({
    type: 1,
    phone: formState.phone
  });
  message.success('手机验证码发送成功！')
  const timer = setInterval(() => {
    phoneCountdown.value--;
    if (phoneCountdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

/**发送邮箱验证码 */
const sendEmailCode = async () => {
  if (!formState.email) {
    message.warning('请输入有效的邮箱地址')
    return;
  }
  emailCountdown.value = 60;
  await accountService.sendEmailCode({
    type: 2,
    email: formState.email
  });
  message.success('邮箱验证码发送成功！')
  const timer = setInterval(() => {
    emailCountdown.value--;
    if (emailCountdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

const showAgreement = () => {
  agreementVisible.value = true;
};

const showPrivacy = () => {
  privacyVisible.value = true;
};

const onFinish = async () => {
  if (!agreement.value) {
    message.warning('请阅读并勾选协议')
    return;
  }
  loading.value = true;
  try {
    await loginService.createRegister(formState as any)
    registerSuccess.value = true
  } catch (error) {
    console.error('注册失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.register-container {
  padding: 24px 30px 32px;
  background-color: #fff;
  height: 100vh;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;

  .logo-area {
    margin-bottom: 16px;
    
    .logo {
      width: 64px;
      height: 64px;
    }
  }

  h2 {
    font-size: 24px;
    color: #1a1a1a;
    margin-bottom: 8px;
    font-weight: 600;
  }

  p {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.form-content {
  :deep(.ant-form-item) {
    margin-bottom: 20px;

    .ant-input-affix-wrapper {
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      transition: all 0.3s;
      background: rgba(255, 255, 255, 0.8);

      &:hover, &:focus {
        border-color: #ee1e52d3;
        box-shadow: 0 0 0 2px rgba(238, 30, 82, 0.1);
      }

      .site-form-item-icon {
        color: #bfbfbf;
      }

      input {
        background: transparent;
      }
    }
  }
}

.verify-code-btn {
  height: 40px;
  border-radius: 8px;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  color: #666;
  transition: all 0.3s;
  padding: 0;

  &:not(:disabled):hover {
    background: #ee1e52d3;
    border-color: #ee1e52d3;
    color: #fff;
  }

  &:disabled {
    background: #f5f5f5;
    color: #999;
  }
}

.agreement-section {
  margin-bottom: 24px;
  text-align: center;
  :deep(.ant-checkbox-wrapper) {
    color: #666;
    &.agreement-error {
      .ant-checkbox-inner {
        border-color: #ff4d4f;
      }
    }

    a {
      color: #ee1e52d3;
      text-decoration: none;
      transition: color 0.3s;

      &:hover {
        color: #d41a49;
      }
    }
  }
}

.submit-btn {
  height: 44px;
  border-radius: 8px;
  background: linear-gradient(135deg, #ee1e52d3, #d41a49);
  border: none;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(238, 30, 82, 0.2);
    background: linear-gradient(135deg, #d41a49, #ee1e52d3);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
