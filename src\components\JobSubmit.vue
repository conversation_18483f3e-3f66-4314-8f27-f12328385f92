<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf(softwareInfo ? softwareInfo.id : 'jobSubmit')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null"
    >
    <div class="container" ref="containerRef" @contextmenu="contextmenu">
      <a-tabs size="small" v-model:activeKey="activeKey" @change="tabChange">
        <template #rightExtra v-if="activeKey == '1'">
          <a-button type="primary" size="small" danger @click="restForm">清空</a-button>&nbsp;
          <a-button type="primary" size="small" :loading="submitLoading" @click="submit">提交作业</a-button>
        </template>
        <a-tab-pane key="1" tab="基础命令">
          <a-row>
            <a-col :span="12">
              <a-form layout="horizontal" ref="formDataRef" :model="formData" :wrapper-col="{ span: 24 }" >
                <a-row>
                  <a-col :span="12">
                    <a-form-item label="作业名称" name="job_name" :rules="[{ required: true, message: '请输入作业名称' }]">
                      <a-input v-model:value="formData.job_name" placeholder="请输入作业名称" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item :label-col="{ span: 7 }" label="集群分区" name="connectId" :rules="[{ required: true, message: '请输入集群分区' }]">
                      <a-select v-model:value="formData.connectId"  placeholder="选择集群分区" @change="changeCluster">
                        <a-select-option v-for="item in store.userInfo.userClusterInfo" :value="item.id">
                          {{  item.clusterName + "-" + item.groupNameDesc }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="12">
                    <a-form-item label="节点数量" name="nodeNum" :rules="[{ required: true, message: '请输入节点数' }]">
                      <a-input type="number" :min="1" :max="20" v-model:value="formData.nodeNum" placeholder="节点数(范围：1-20)" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item :label-col="{ span: 7 }" label="核心数" name="cpuNum" :rules="[{ required: true, message: '请输入核心数' }]">
                      <a-input type="number" :min="1" :max="192" v-model:value="formData.cpuNum" placeholder="核心数(范围：1-192)" />
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row> 
                  <a-col :span="24">
                    <a-form-item :label-col="{ span: 3.5 }" label="算例目录" name="current_working_directory" :rules="[{ required: true, message: '请输入操作目录' }]">
                      <a-input v-model:value="formData.current_working_directory" placeholder="请输入操作目录" />
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-form-item :label-col="{ span: 3.5 }" label="环境变量" name="environment" :rules="[{ required: true, message: '请输入环境变量' }]">
                  <div class="env-list" :style="{ height: `${tableScrolly - 180}px` }">
                    <p v-for="(item, index) of formData.environment">
                      <span>{{ item }}</span>
                      &nbsp;
                      <DeleteOutlined @click="clearEnvironment(index)" style="color: red; cursor: pointer;" />
                    </p>
                  </div>
                  <a-input v-model:value="environmentValue" placeholder="请输入环境变量">
                    <template #addonAfter>
                      <a-button size="small" type="link" @click="addEnvironment">添加</a-button>
                    </template>
                  </a-input>
                </a-form-item>
                <!-- <a-form-item :label-col="{ span: 3.5 }" label="执行命令" name="script" :rules="[{ required: true, message: '请输入执行命令' }]">
                  <a-textarea type="textarea" :rows="4" v-model:value="formData.script" placeholder="请输入执行命令" />
                </a-form-item> -->
              </a-form>
            </a-col>
            <a-col :span="12">
              <div class="edit-title">执行命令</div>
              <div class="edit-box" ref="editRef" :style="{ height: `${tableScrolly}px` }"></div>
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane key="2" tab="历史记录">
          <a-table
            size="small"
            rowKey="text"
            :bordered="true"
            :loading="loading"
            :scroll="{ y: tableScrolly }"
            :columns="columns.map(column => ({...column, align: 'center'}))"
            :data-source="dataList"
            :pagination="{
              current: searchData.pageNo,
              pageSize: searchData.pageSize,
              total: total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: any) => `共 ${total} 条`,
            }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'environment'">
                <a-tooltip :title="record.environment">
                  <p class="text_ov4" v-for="item of record.environment">
                    <span>{{ truncateText(item, 20) }}</span>
                  </p>
                </a-tooltip>
              </template>
              <template v-if="column.key === 'script'">
                <a-tooltip :title="record.script">
                  <span>{{ truncateText(record.script, 20) }}</span>
                </a-tooltip>
              </template>
              <template v-if="column.key === 'operate'">
                <a-button v-if="!record.favorite" size="small" type="link" @click="setJobState(record)">
                  {{ record.favorite ? "取消收藏" : "收藏" }}
                </a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="3" tab="常用命令">
          <a-table
            size="small"
            rowKey="text"
            :bordered="true"
            :loading="favoriteLoading"
            :scroll="{ y: tableScrolly }"
            :columns="favoriteColumns.map(column => ({...column, align: 'center'}))"
            :data-source="favoriteList"
            :pagination="{
              current: favoriteSearchData.pageNo,
              pageSize: favoriteSearchData.pageSize,
              total: favoriteTotal,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (favoriteTotal: any) => `共 ${favoriteTotal} 条`,
            }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'environment'">
                <a-tooltip :title="record.environment">
                  <p class="text_ov4" v-for="item of record.environment">
                    <span>{{ truncateText(item, 20) }}</span>
                  </p>
                </a-tooltip>
              </template>
              <template v-if="column.key === 'script'">
                <a-tooltip :title="record.script">
                  <span>{{ truncateText(record.script, 20) }}</span>
                </a-tooltip>
              </template>
              <template v-if="column.key === 'operate'">
                <a-button size="small" type="link" @click="usedScript(record)">应用</a-button>
                <a-button size="small" type="text" danger @click="setJobState(record)">移除</a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
    <template #title>
      <div class="modal-head" ref="jobmodalRef">
        <div class="title-text">{{ softwareInfo?.name || '提交作业'}}</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex(softwareInfo ? softwareInfo.id : 'jobSubmit')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, computed } from 'vue'
import { LineOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { useResizeObserver  } from '@vueuse/core'
import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker";
import * as monaco from 'monaco-editor';
import { useDrag } from '../views/hooks/useDrag'
import { message } from 'ant-design-vue';
import workService from '@/api/work'
import { useStore } from '@/store';
import { contextmenu } from '@/utils/index'
import { Modal } from 'ant-design-vue';
const props = defineProps<{ levelList: string[], changeLevelIndex:Function, softwareInfo?: any }>();
const emit = defineEmits(['close']);
const store = useStore();
const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽
const jobmodalRef = ref();
const transformStyle = ref(useDrag(jobmodalRef));

const containerRef = ref();
const tableScrolly = ref(0);

// 编辑器实例
let editor: any;
const editRef = ref();
onMounted(async () => {
  store.working.push(props.softwareInfo ? props.softwareInfo.id : 'jobSubmit');
  await getJobHistoryList();
  nextTick(() => {
    useResizeObserver(containerRef.value, (entries) => { //监听终端窗口
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - 140;
    });
  });
  createMonacoEditor();
})

/**编辑器初始化 */
function createMonacoEditor() {
  window.MonacoEnvironment = {
    getWorker(_, _label) {
      return new editorWorker();
    },
  };
  editor = monaco.editor.create(editRef.value, {
    value: '',
    // language: 'javascript',
    theme: 'vs-dark',
    automaticLayout: true,
  })
  editor.setValue(props.softwareInfo?.script || '');
  editor.getModel()?.setEOL(monaco.editor.EndOfLineSequence.LF);
}

/**基础信息 */
const formData = ref({
  current_working_directory: '',
  environment: props.softwareInfo?.environment || [
    'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin:/home/<USER>/spack/bin:/usr/share/Modules/bin',
  ],
  job_name: '',
  script: props.softwareInfo?.script || '',
  cpuNum: '',
  nodeNum: '',
  clusterId: null,
  connectId: null,
  // partition: null
});

// 当前集群分区
const clusterInfo = computed(() => {
  return store.userInfo.userClusterInfo.find((item: any) => item.id == formData.value.connectId) || {};
})

/**选择集群分区 */
function changeCluster() {
  formData.value.clusterId = clusterInfo.value.clusterId;
}

const environmentValue = ref();
/*添加环境变量 */
function addEnvironment() {
  if(!environmentValue.value) return;
  formData.value.environment.push(environmentValue.value);
  environmentValue.value = '';
}

/**清除环境 */
function clearEnvironment(index: number) {
  formData.value.environment.splice(index, 1)
}

const formDataRef = ref();
const submitLoading = ref(false);
/**提交作业 */
async function submit() {
  try {
    await formDataRef.value.validate()
    submitLoading.value = true;
    formData.value.script = editor.getValue();
    if (formData.value.script.trim() == '') {
      submitLoading.value = false;
      return message.warning('请输入执行命令！');
    }
    // 提交集群分区是否欠费
    await checkCoreTime();
    // 提交
    await workService.createdJobInit(store.authInfo.tenantCode, formData.value);
    message.success('提交成功');
    tabChange(activeKey.value);
    submitLoading.value = false;
  } catch (error) {
    submitLoading.value = false;
  }
}

/**重置 */
function restForm() {
  formData.value = {
    current_working_directory: '',
    environment: [],
    job_name: '',
    script: '',
    cpuNum: '',
    nodeNum: '',
    clusterId: null,
    connectId: null,
    // partition: null
  }
}

const errorTips = ref<{[key: string]: boolean}>({})
/**核时监听-是否欠费 */
function checkCoreTime() {
  return new Promise((resolve, reject) => {
    const clusterInfo = store.userInfo.userClusterInfo.find((item: { id: string; }) => item.id == formData.value.connectId);
    if(clusterInfo) {
      // 主账户/子账户剩余核时
      const usedCoreTime = store.userInfo.roleType == 2 ? Number(clusterInfo.residueCoreTime) : Number(clusterInfo.parentCanUsedCoreTime)
      if (usedCoreTime <= 0 && !errorTips.value[clusterInfo.id]) {
        Modal.error({
          class: 'remind-modal',
          title: '核时欠费',
          content: '您当前提交的集群分区余额核时不足，无法提交作业，请及时联系管理员充值',
          onOk: () => {
            delete errorTips.value[clusterInfo.id];
          },
          getContainer: () => containerRef.value
        });
        errorTips.value[clusterInfo.id] = true
        return reject(false);
      }
      resolve(true);
    };
  })
}

/**打开弹窗 */
async function open() {
  showModal.value = true;
}

/**隐藏弹窗 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  restForm();
  store.working.splice(store.working.indexOf(props.softwareInfo ? props.softwareInfo.id : 'jobSubmit'), 1);
  emit('close');
};

/**切换列表 */
function tabChange(value: string) {
  activeKey.value = value;
  value == '1' ? getJobHistoryList() : getJobFavoriteList();
}

const loading = ref(false);
const activeKey = ref('1');
const dataList = ref<any>([]);
const total = ref(0);
const searchData = ref({
  pageNo: 1,
  pageSize: 20,
  softwareId: props.softwareInfo ? props.softwareInfo.id : null,
  jobName: '',
  userName: '',
  startCreateTime: '',
  endCreateTime: '',
  tenantCode: store.authInfo.tenantCode,
  tenant: '',
});
/**表格 */
const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '用户名', dataIndex: 'userName', key: 'userName'},
  { title: '作业名', dataIndex: 'jobName', key: 'jobName' },
  // { title: '租户名', dataIndex: 'tenantName', key: 'tenantName'},
  { title: '工作目录', dataIndex: 'currentWorkingDirectory', key: 'currentWorkingDirectory' },
  { title: '分区', dataIndex: 'partitionName', key: 'partitionName' },
  { width: 160, title: '环境变量', dataIndex: 'environment', key: 'environment'},
  { width: 160, title: '执行命令', dataIndex: 'script', key: 'script'},
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime'},
  { width: 60, title: '操作', dataIndex: 'operate', key: 'operate'},
];

/**获取历史作业记录 */
async function getJobHistoryList() {
  try {
    loading.value = true;
    const { data } = await workService.getJobHistoryList(store.authInfo.tenantCode, searchData.value);
    dataList.value = data.list || [];
    total.value = data.total;
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false;
  }
}

/**设置收藏 */
async function setJobState(item: any) {
  try {
    loading.value = true;
    await workService.setJobState(store.authInfo.tenantCode, {
      id: item.id,
      state: !item.favorite
    });
    message.success('设置成功')
    tabChange(activeKey.value);
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false;
  }
}

/**应用回显 */
function usedScript(item: any) {
  formData.value = {
    current_working_directory: item.currentWorkingDirectory,
    environment: item.environment,
    job_name: item.jobName,
    script: item.script,
    cpuNum: item.cpuNum,
    nodeNum: item.nodeNum,
    connectId: null,
    // partition: item.partitionName,
    clusterId: null
  };
  editor.setValue(item.script || '');
  activeKey.value = '1'
}

const favoriteLoading = ref(false);
const favoriteList = ref<any>([]);
const favoriteTotal = ref(0);
const favoriteSearchData = ref<any>({
  pageNo: 1,
  pageSize: 20,
  jobName: '',
  userName: '',
  startCreateTime: '',
  endCreateTime: '',
  tenantCode: store.authInfo.tenantCode,
  tenant: '',
});
/**表格 */
const favoriteColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '作业名', dataIndex: 'jobName', key: 'jobName' }, 
  { title: '工作目录', dataIndex: 'currentWorkingDirectory', key: 'currentWorkingDirectory' },
  { title: '分区', dataIndex: 'partitionName', key: 'partitionName' },
  { width: 200, title: '环境变量', dataIndex: 'environment', key: 'environment'},
  { width: 200, title: '执行命令', dataIndex: 'script', key: 'script'},
  { width: 110, title: '操作', dataIndex: 'operate', key: 'operate'},
];

/**获取收藏记录 */
async function getJobFavoriteList() {
  try {
    loading.value = true;
    const { data } = await workService.getJobFavoriteList(store.authInfo.tenantCode, favoriteSearchData.value as any);
    favoriteList.value = data.list || [];
    favoriteTotal.value = data.total;
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false;
  }
}

// 截取字符串
const truncateText = (text: string, maxLength: number) => {
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};
defineExpose({open, close, name: props.softwareInfo ? props.softwareInfo.id : 'jobSubmit'})
</script>
<style scoped lang="scss">
@import url(./antdmodal.scss);
.container {
  width: 100%;
  height: 100%;
  padding: 0px 24px 24px 24px;
  overflow-y: scroll;
  position: relative;
  .env-list {
    width: 100%;
    height: 240px;
    border: 1px solid #dbdbdb;
    background-color: #f1f1f1;
    border-radius: 4px;
    margin-bottom: 6px;
    padding: 4px 8px;
    overflow-y: scroll;
  }
  .edit-title {
    margin-left: 20px;
    padding: 0 20px;
    background-color: #000;
    color: #fff;
  }
  .edit-box {
    height: 420px;
    margin-left: 20px;
  }
}

// 核时提醒
:deep(.remind-modal .ant-modal-content){
  padding: 20px 24px!important;
  font-weight: 500;
}
:deep(.remind-modal .ant-modal-header){
  border-bottom: none;
}
</style>
