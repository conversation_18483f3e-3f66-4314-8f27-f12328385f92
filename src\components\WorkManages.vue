<template>
  <a-modal
    style="top: 6%;"
    :zIndex="levelList.indexOf('work')"
    :maskClosable="false"
    :wrap-style="{ pointerEvents: 'none', overflow: 'hidden' }" 
    :mask="false"
    :wrapClassName="isFullModal ? 'full-modal' : ''" 
    :width="isFullModal ? '100%' : '75%'"
    :bodyStyle="{ height: isFullModal ? '100vh' : '75vh' }"
    @cancel="close" 
    :open="showModal" 
    :footer="null">
    <div class="container" ref="containerRef" @contextmenu.prevent="showMenu($event)" @click="isShowMenu = false">
      <div class="card-head" ref="cardHeadRef">
        <a-form
          size="small"
          layout="inline"
          :model="searchData"
          name="basic"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18}"
          autocomplete="off"
        >
          <a-form-item label="集群" style="width: 160px">
            <a-select size="small" v-model:value="searchData.clusterId" @change="getJobList"  placeholder="选择集群">
              <a-select-option v-for="item in clusterList" :value="item.clusterId">
                {{  item.clusterName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="作业名称">
            <a-input v-model:value="searchData.jobName" placeholder="请输入作业名称" @input="debouncedGetJobList" />
          </a-form-item>
          <a-form-item label="提交时间">
            <a-range-picker v-model:value="dateArr" valueFormat="YYYY-MM-DD" @change="dateChange" />
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchData.state" style="width: 160px" allowClear placeholder="请选择状态" @change="getJobList">
              <a-select-option v-for="(item, index) in stateArr" :value="index">{{ item.text }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button size="small" type="primary" @click="getJobList">
              <ReloadOutlined />刷新
            </a-button>
          </a-form-item>
        </a-form>
        <div class="state-list">
          <p v-for="item in stateArr" class="text_ov1">
            <span class="state-black" :style="{background: item.color}"></span> 
            <span class="state-text">{{ item.tag }} {{ item.text }}</span>
          </p>
        </div>
      </div>
      <div class="card">
        <a-table
          size="small"
          rowKey="text"
          :bordered="true"
          :loading="loading"
          :scroll="{ x: 1500,  y: tableScrolly }"
          :columns="columns.map(column => ({...column, align: 'center'}))"
          :data-source="dataList"
          :pagination="{
            current: searchData.pageNo,
            pageSize: searchData.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: any) => `共 ${total} 条`,
            onChange: handlePageChange,
            onShowSizeChange: handleSizeChange,
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'nickName'">
              <p>{{ record.nickName }}</p>
              <a-tag v-if="store.userInfo.roleType == 2 && record.roleType == 3" style="margin: 0;" :bordered="false" color="#999999">子用户</a-tag>
            </template>
            <template v-if="column.key === 'jobName'">
              <a-button v-if="record.jobName == 'vnc' && [0,1].includes(record.state) && record.vncUrl" size="small" type="link" @click="openVnc(record)">
                <DesktopOutlined style="vertical-align: middle;" />
                <span>{{ record.jobName }}</span>
              </a-button>
              <p v-else>{{ record.jobName }}</p>
            </template>
            <template v-if="column.key === 'state'">
              <p>
                <span v-if="stateList[record.state]" :style="{color: stateList[record.state].color}">
                  {{ stateList[record.state].tag }}
                </span>
                <span v-else>未知状态</span>
              </p>
              <a-button v-if="[0,1].includes(record.state) && (record.roleType == store.userInfo.roleType)" size="small" type="text" danger @click="endJob(record)">取消作业</a-button>
            </template>
            <template v-if="column.key === 'timeStartFormat'">
              <span>{{ record.timeStartFormat == 0 ? '--' :  record.timeStartFormat}}</span>
            </template>
            <template v-if="column.key === 'runningTime'">
              <span v-html="formatSeconds(record.runningTime)"></span>
            </template>
            <template v-if="column.key === 'workDir'">
              <a-tooltip :title="record.workDir">
                <span>{{ truncateText(record.workDir, 20) }}</span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'remainingCoreTime'">
              <span v-if="record.roleType == 3">--</span>
              <span v-else v-html="formatSeconds(record.remainingCoreTime)"></span>
            </template>
            <template v-if="column.key === 'parentRemainingCoreTime'">
              <span v-if="record.roleType == 3" v-html="formatSeconds(record.parentRemainingCoreTime)"></span>
              <span v-else>--</span>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <template #title>
      <div class="modal-head" ref="headModalRef">
        <div class="title-text">作业管理</div>
        <LineOutlined class="small-icon" @click="hide" />
        <img class="big-icon" src="@/assets/image/maxwindow.png" @click="isFullModal = !isFullModal" alt="">
      </div>
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="!isFullModal ? transformStyle : ''" @click="changeLevelIndex('work')">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
  <ContextMenu v-if="isShowMenu" :menuPosition="menuPosition" :menus="menus" :transferFile="[]" @menuChange="menuChange"></ContextMenu>
</template>
<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { LineOutlined, ReloadOutlined, DesktopOutlined } from '@ant-design/icons-vue';
import { useStore } from '@/store'
import { useResizeObserver  } from '@vueuse/core'
import { useDrag } from '../views/hooks/useDrag'
import { formatSeconds } from '@/utils/index';
import ContextMenu from './ContextMenu.vue'
import workService from '../api/work'
import { debounce } from 'lodash'; 
import { message, Modal } from 'ant-design-vue';
defineProps<{ levelList: string[], changeLevelIndex:Function }>();
const emit = defineEmits(['close'])
const store = useStore();

const clusterList = computed(() => {
  return [...new Map(store.userInfo.userClusterInfo.map((item: any) => [item.clusterId, item])).values()] as any[];
});

const showModal = ref(true);
// 全屏
const isFullModal = ref(false);
// 拖拽终端
const headModalRef = ref();
const transformStyle = ref(useDrag(headModalRef));
/**表格 */
const columns = [
  // { title: '集群账户', dataIndex: 'account', key: 'account' },
  { title: '作业号', dataIndex: 'idJob',  key: 'idJob'},
  { title: '姓名', dataIndex: 'nickName', key: 'nickName' },
  { title: '作业名', dataIndex: 'jobName', key: 'jobName' },
  { title: '集群分组', dataIndex: 'groupNameDesc', key: 'groupNameDesc'},
  { title: '作业分区', dataIndex: 'partition', key: 'partition'},
  { title: '工作目录', dataIndex: 'workDir', key: 'workDir' },
  { title: '节点', dataIndex: 'nodelist', key: 'nodelist' },
  { width: 60, title: '核数', dataIndex: 'cpuCores', key: 'cpuCores'},
  { title: '作业状态', dataIndex: 'state', key: 'state'},
  { title: '提交时间', dataIndex: 'timeSubmitFormat', key: 'timeSubmitFormat'},
  { title: '开始运行时间', dataIndex: 'timeStartFormat', key: 'timeStartFormat'},
  { title: '结束运行时间', dataIndex: 'timeEndFormat', key: 'timeEndFormat'},
  { title: '运行时长', dataIndex: 'runningTime', key: 'runningTime'},
  { title: '剩余核时', dataIndex: 'remainingCoreTime', key: 'remainingCoreTime'},
  { width: 120, title: '主账户剩余核时', dataIndex: 'parentRemainingCoreTime', key: 'parentRemainingCoreTime'},
];

const stateList: any = {
  0: {tag: "pending", text: "排队等待执行", color: "#365dea"},
  1: {tag: "running", text: "已分配资源并正在执行", color: "#1ddd8b"},
  2: {tag: "suspended", text: "已分配资源但执行被暂停", color: "#ddd815"},
  3: {tag: "complete", text: "成功完成执行", color: "#1677ff"},
  4: {tag: "cancelled", text: "被用户取消", color: "#e36e1c"},
  5: {tag: "failed", text: "执行失败", color: "#ec0e0e"},
  6: {tag: "timeout", text: "达到时间限制而终止", color: "#c732ea"},
  7: {tag: "node_fail", text: "由于节点故障而终止", color: "#fc5d5d"},
  8: {tag: "preempted", text: "由于抢占而终止", color: "#c47a40"},
  9: {tag: "boot_fail", text: "由于节点启动失败而终止", color: "#f37b4d"},
  10: {tag: "deadline", text: "达到截止时间而终止", color: "#f79334"},
  11: {tag: "oom", text: "遇到内存不足错误", color: "#f53a00"},
  12: {tag: "end", text: "不是真实状态,表示枚举结束", color: "#c00000"},
  13: {tag: "default", text: "未知状态", color: "#be5655"},
};
const stateArr: any = computed(() => Object.values(stateList))
/**搜索 */
const dateArr = ref([]);
const searchData = ref({
  pageNo: 1,
  pageSize: 20,
  jobName: '',
  startTime: '',
  endTime: '',
  state: null,
  tenant: '',
  clusterId: store.userInfo.userClusterInfo[0].clusterId || "",
});

const containerRef = ref();
const cardHeadRef = ref();
/**表格自适应高度 */
const tableScrolly = ref(400);
onMounted(async () => {
  store.working.push('work');
  await getJobList();
  nextTick(() => {
    useResizeObserver(containerRef.value, (entries) => { //监听窗口
      if(!containerRef.value) return;
      if(!cardHeadRef.value) return;
      const { height } = entries[0].contentRect;
      tableScrolly.value = height - (cardHeadRef.value.offsetHeight + 140);
    });
  })
});

const loading = ref(false);
const dataList = ref<any>([]);
// 截取字符串
const truncateText = (text: string, maxLength: number) => {
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};
// 防抖
const debouncedGetJobList = debounce(getJobList, 300);

/**日期筛选 */
function dateChange() {
  if (!dateArr.value) {
    dateArr.value = [];
    searchData.value.startTime = '';
    searchData.value.endTime = '';
  } else {
    searchData.value.startTime = dateArr.value[0] + ' 00:00:00';
    searchData.value.endTime = dateArr.value[1] + ' 23:59:59';
  }

  getJobList();
}

const total = ref(0);
/**获取作业管理 */
async function getJobList() {
  loading.value = true;
  try {
    const { data } = await workService.getJobList(store.authInfo.tenantCode, searchData.value as any);
    dataList.value = data.list;
    total.value = data.total;
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false;
  }
}

/**取消作业 */
function endJob(item: any) {
  Modal.confirm({
    title: '提示',
    content: `确定要取消${item.jobName}该作业吗`,
    onOk: async () => {
      return workService.endJob(store.authInfo.tenantCode, item.idJob, searchData.value.clusterId).then(() => {
        message.success('取消成功');
        getJobList();
      })
    }
  })
}

/**右键菜单 */
const isShowMenu = ref(false);
const menuPosition = ref({x: 0, y: 0});
const menus = ref(['refresh']);
function showMenu(event: any) {
  menuPosition.value.x = event.clientX;
  menuPosition.value.y = event.clientY;
  isShowMenu.value = true;
  event.stopPropagation();
}

/**右键菜单 */
function menuChange({ menuType }: any) {
  switch(menuType) {
    case 'refresh' : getJobList(); break;
  };
  isShowMenu.value = false;
}

/**打开vnc */
function openVnc(item: any) {
  // 桌面应用跳转浏览器
  if(window.electronAPI) {
    window.electronAPI.sendMessageToMain({ type: 'download', url: item.vncUrl  });
  } else {
    window.open(item.vncUrl, '_blank');
   }
}

/**打开作业管理 */
function open() {
  showModal.value = true;
}

/**隐藏 */
function hide() {
  showModal.value = false;
}

/**关闭弹窗 */
function close() {
  showModal.value = false;
  store.working.splice(store.working.indexOf('work'), 1);
  emit('close')
};

defineExpose({open, close})

/**页改变 */
function handlePageChange(page: number) {
  searchData.value.pageNo = page;
  getJobList();
}

/**页数改变 */
function handleSizeChange(current: number, size: number) {
  searchData.value.pageNo = current;
  searchData.value.pageSize = size;
  getJobList();
}
</script>

<style scoped lang="scss" >
@import url(./antdmodal.scss);
.container {
  width: 100%;
  margin-top: 12px;
  height: 100%;
  .card-head {
    padding: 0 20px;
    .state-list {
      margin-top: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      & p {
        width: 25%;
      }
      .state-black {
        margin-right: 6px;
        display: inline-block;
        width: 10px;
        height: 10px;
        background: #000;
      }
      .state-text {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
  :deep(.ant-page-header) {
    padding: 8px 24px;
  }
  .card {
    padding: 20px;
    :deep(.ant-table-tbody>tr>td) {
      padding-top: 4px;
      padding-bottom: 4px;
    }
    .file-name {
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
</style>
