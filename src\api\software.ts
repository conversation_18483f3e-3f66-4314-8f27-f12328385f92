import { request } from '@/utils/request.ts'

export default {
  /**
	 * 软件列表
	 * @param {object} params
	 */
	getSoftwareList(tenant: string, params: {
    pageNo: number,
    pageSize: number,
    name: string,
  }) {
	  return request({ url: `/software/v1.0/${tenant}/user/software`, 	method: 'get', params: params	 });
	},

  /**
	 * 添加软件
	 * @param {object} params
	 */
	setSoftwareDesk(tenant: string, params: {
    id: string,
    state: boolean
  }) {
	  return request({ url: `/software/v1.0/${tenant}/software-to-desk/${params.id}/${params.state}`, 	method: 'put'	 });
	},
}