<template>
  <div class="box" id="list">
    <ul class="box_left">
      <li :class="{'active' :activeIndex == index}" v-for="(item, index) in catList" @click="change(index)">
        <img src="https://phadwiki-1308066085.cos.ap-chengdu.myqcloud.com/assets/images/course_all.png" alt="icon">
        <span>{{ item.name }}</span>
      </li>
    </ul>
    <ul class="box_right" ref="scrollList" v-on:scroll="onScroll">
      <li v-for="item in tableList" :key="item.id" @click="toCourseInfo(item)">
        <img :src="item.cover":alt="item.name">
        <p>{{ item.name }}</p>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useScroll } from '@vueuse/core'
import { useStore } from '@/store'
import courseService from "@/api/course";
const emit = defineEmits(['change'])
const store = useStore();
const activeIndex = ref(0);
const catList = ref([
  { name: '全部课程', type: '0' },
  { name: '精选课程', type: '1' },
  { name: '最新课程', type: '2' },
  { name: '热门课程', type: '3' },
]);

onMounted(() => {
  getList();
});


const tableList = ref();
const page = ref(1);
const pageSize = ref(20);
const total = ref(0);
/**获取课程列表 */
async function getList(more?: boolean) {
  const params = {
    type: catList.value[activeIndex.value].type,
    pageNo: page.value,
    pageSize: pageSize.value,
  }
  const { data } = await courseService.getCourseList(store.authInfo.tenantCode, params);
  if (more) {
    tableList.value = [...tableList.value, ...data.list]
  } else {
    tableList.value = data.list;
  }
  total.value = data.total
}

const scrollList = ref<HTMLElement | null>(null);
/**监听滚动 */
function onScroll() {
  const { arrivedState } = useScroll(scrollList.value);
  if (arrivedState.bottom && total.value > tableList.value.length) {
    page.value ++;
    getList(true);
  }
}

/**切换 */
function change(index: number) {
  activeIndex.value = index;
  page.value = 1;
  getList();
}

/*跳转详情 */
function toCourseInfo(item: any) {
  emit('change', {isPlay: true,  courseInfo: item})
}
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  padding: 30px;
  background: #edeef0;
}

.box_left {
  list-style: none;
  display: flex;
  flex-direction: column;
  width: 180px;
  padding-top: 30px;
  background: #fff;
  border-radius: 10px;

}

.box_right {
  flex: 1;
  list-style: none;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-left: 30px;
  overflow-y: auto;
  background: #fff;
  border-radius: 10px;
  padding: 0 30px 30px 0;
}

.box_left>li {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 23px;
  height: 53px;
  cursor: pointer;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #303133;

}

.box_left>li>span {
  margin-left: 6px;
}

.box_left .active {
  background: #ECF5FF;
  color: #409eff;
}

.box_right>li {
  width: 290px;
  height: 268px;
  background: #FFFFFF;
  box-shadow: 0 0 10px 0 rgb(0 0 0 / 25%);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 30px;
  margin-top: 34px;
  cursor: pointer;
}

.box_right>li>img {
  width: 290px;
  height: 207px;
  object-fit: contain;
}

.box_right>li>p {
  height: 60px;
  line-height: 60px;
  padding: 0 18px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>