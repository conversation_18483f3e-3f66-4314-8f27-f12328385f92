<template>
  <div class="container">
    <div class="nav-title-wrapper">
      <h2>登录日志</h2>
      <p>记录每次登录时间、用户的列表</p>
    </div>
    <div class="nav-content">
      <a-table
        size="small"
        rowKey="text"
        :loading="loading"
        :scroll="{ x: 500, y: tableScrolly }"
        :columns="columns.map(column => ({...column, align: 'center'}))"
        :data-source="dataList"
        :bordered="true"
        :pagination="{
          current: searchData.pageNo,
          pageSize: searchData.pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: any) => `共 ${total} 条`,
          onChange: handlePageChange,
          onShowSizeChange: handleSizeChange,
        }"
      >
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import loginService from '@/api/login'
  import { useStore } from '@/store';

  defineProps<{ tableScrolly: number }>()
  const store = useStore();

  /**表格 */
  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '用户名', dataIndex: 'userName', key: 'userName' },
    // { title: '租户名称', dataIndex: 'tenantName',  key: 'tenantName'},
    // { title: '租户code', dataIndex: 'tenantCode', key: 'tenantCode' },
    { title: '登录时间', dataIndex: 'loginTime', key: 'loginTime'},
  ];

  const searchData = ref<any>({
    pageNo: 1,
    pageSize: 20,
    userId: store.authInfo.id,
    userName: "",
    endLoginTime: "",
    startLoginTime: "",
    tenantCode: store.authInfo.tenantCode,
    tenantName: ""
  });
  const loading = ref(false);
  const dataList = ref<any>([]);
  const total = ref(0);
  /**获取登录日志 */
  async function getLoginLogs() {
    loading.value = true;
    try {
      const { data } = await loginService.getLoginLogs(store.authInfo.tenantCode, searchData.value);
      dataList.value = data.list;
      total.value = data.total;
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false;
    }
  }

  /**页改变 */
  function handlePageChange(page: number) {
    searchData.value.pageNo = page;
    getLoginLogs();
  }

  /**页数改变 */
  function handleSizeChange(current: number, size: number) {
    searchData.value.pageNo = current;
    searchData.value.pageSize = size;
    getLoginLogs();
  }

  onMounted(() => {
    getLoginLogs()
  })
</script>

<style lang="scss" scoped>
  .container {
    height: 100%;
    .nav-title-wrapper {
      height: 120px;
      background-color: #fff;
      border-radius: 6px;
      padding: 10px 20px;
      h2 {
        font-weight: bold;
      }
      p {
        font-size: 16px;
      }
    }
    .nav-content {
      padding: 10px 20px;
      margin-top: 10px;
      height: calc(100% - 10px - 120px);
      background-color: #fff;
      border-radius: 6px;
    }
  }
</style>