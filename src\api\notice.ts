import { request } from '@/utils/request.ts'

export default {
  /**
	 * 消息列表
	 * @param {object} params
	 */
	getNoticeList(tenant: string, params: {
    pageNo: number,
    pageSize: number,
  }) {
	  return request({ url: `/notice/v1.0/${tenant}/list`, 	method: 'get', params: params	 });
	},

  /**
	 * 未读消息数
	 */
	// getNoticeCount(tenant: string) {
	//   return request({ url: `/notice/v1.0/${tenant}/count`, 	method: 'get'	});
	// },

  /**
	 * 消息已读
	 */
	readNotice(tenant: string, id: string) {
	  return request({ url: `/notice/v1.0/${tenant}/read/${id}`, 	method: 'get'	});
	},
  
  /**
	 * 消息全读
	 */
	readNoticeAll(tenant: string) {
	  return request({ url: `/notice/v1.0/${tenant}/read-all`, 	method: 'get'	});
	},

	/**
	 * 删除消息
	 */
	delNotice(tenant: string, ids: string[]) {
	  return request({ url: `/notice/v1.0/${tenant}/read`, 	method: 'delete', params: {ids: ids.join(',')}	});
	},
}